package service

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"cloud.google.com/go/firestore"
	"github.com/vima-inc/derental/external/openrouter"
	"github.com/vima-inc/derental/models"
)

// AdminSignIn validates admin credentials with domain restriction
func (s *Service) AdminSignIn(ctx context.Context, email, password string) (models.AuthResult, error) {
	// Validate domain
	if !strings.HasSuffix(email, "@derentalequipment.com") {
		return models.AuthResult{}, fmt.Errorf("access restricted to @derentalequipment.com domain")
	}

	// Use existing SignIn method with Firebase Authentication
	input := models.SignInInput{
		Email:    email,
		Password: password,
	}

	authResult, err := s.SignIn(ctx, input)
	if err != nil {
		return models.AuthResult{}, fmt.Errorf("authentication failed: %w", err)
	}

	// Add admin flag to response
	authResult.UserType = "admin"

	return authResult, nil
}

// AdminDashboardStats represents dashboard statistics
type AdminDashboardStats struct {
	TotalMasterInventory int `json:"total_master_inventory"`
	TotalEquipments      int `json:"total_equipments"`
	TotalEquippers       int `json:"total_equippers"`
	TotalLodgers         int `json:"total_lodgers"`
	TotalBookings        int `json:"total_bookings"`
}

// GetAdminDashboardStats returns statistics for admin dashboard with caching
func (s *Service) GetAdminDashboardStats(ctx context.Context) (AdminDashboardStats, error) {
	const cacheKey = "admin_dashboard_stats"

	// Try to get from cache first
	if s.cache != nil {
		log.Printf("Cache is available, checking for key: %s", cacheKey)
		if cachedStats, ok := s.cache.Get(cacheKey); ok {
			log.Printf("Admin dashboard stats served from cache")
			return cachedStats.(AdminDashboardStats), nil
		}
		log.Printf("Cache miss for key: %s", cacheKey)
	} else {
		log.Printf("Cache is not available")
	}

	log.Printf("Admin dashboard stats cache miss, computing fresh stats")

	// Cache miss or no cache available, compute stats
	stats := AdminDashboardStats{}

	// Get master inventory count
	masterInventory, err := s.GetAllToolerBidzEquipment(ctx)
	if err != nil {
		return stats, fmt.Errorf("unable to get master inventory: %w", err)
	}
	stats.TotalMasterInventory = len(masterInventory)

	// Get total equippers count
	equippers, err := s.db.GetAllEquipper(ctx)
	if err != nil {
		return stats, fmt.Errorf("unable to get equippers: %w", err)
	}
	stats.TotalEquippers = len(equippers)

	// Get total lodgers count
	lodgers, err := s.db.GetAllLodgers(ctx)
	if err != nil {
		return stats, fmt.Errorf("unable to get lodgers: %w", err)
	}
	stats.TotalLodgers = len(lodgers)

	// Get total equipments count
	equipmentCount, err := s.db.CountAllEquipments(ctx)
	if err != nil {
		return stats, fmt.Errorf("unable to count equipments: %w", err)
	}
	stats.TotalEquipments = equipmentCount

	// For now, set bookings to 0 since we don't have a count method for them yet
	// This could be implemented later with proper counting methods
	stats.TotalBookings = 0

	// Cache the computed stats
	if s.cache != nil {
		// Use cache cost of 1 (small object)
		s.cache.Set(cacheKey, stats, 1)
		log.Printf("Admin dashboard stats cached")
	}

	return stats, nil
}

// ClearAdminDashboardStatsCache clears the cached admin dashboard statistics
// This can be called when data changes that would affect the stats
func (s *Service) ClearAdminDashboardStatsCache() {
	const cacheKey = "admin_dashboard_stats"
	if s.cache != nil {
		s.cache.Del(cacheKey)
	}
}

// GetAllARALevel1Categories returns all ARA Level 1 categories with caching
func (s *Service) GetAllARALevel1Categories(ctx context.Context) ([]models.ARALevel1Category, error) {
	const cacheKey = "ara_level1_categories"

	// Try to get from cache first
	if s.cache != nil {
		if cachedCategories, ok := s.cache.Get(cacheKey); ok {
			return cachedCategories.([]models.ARALevel1Category), nil
		}
	}

	// Cache miss or no cache available, fetch using ARA taxonomy service
	firestoreClient, ok := s.db.GetFirestoreClient().(*firestore.Client)
	if !ok {
		return nil, fmt.Errorf("unable to get firestore client")
	}

	araTaxonomyService := NewARATaxonomyService(firestoreClient)
	categories, err := araTaxonomyService.GetAllLevel1Categories(ctx)
	if err != nil {
		return nil, err
	}

	// Cache the result
	if s.cache != nil {
		s.cache.Set(cacheKey, categories, 1)
	}

	return categories, nil
}

// GetAllARALevel2Types returns all ARA Level 2 types with caching
func (s *Service) GetAllARALevel2Types(ctx context.Context) ([]models.ARALevel2Type, error) {
	const cacheKey = "ara_level2_types"

	// Try to get from cache first
	if s.cache != nil {
		if cachedTypes, ok := s.cache.Get(cacheKey); ok {
			return cachedTypes.([]models.ARALevel2Type), nil
		}
	}

	// Cache miss or no cache available, fetch using ARA taxonomy service
	firestoreClient, ok := s.db.GetFirestoreClient().(*firestore.Client)
	if !ok {
		return nil, fmt.Errorf("unable to get firestore client")
	}

	araTaxonomyService := NewARATaxonomyService(firestoreClient)
	types, err := araTaxonomyService.GetAllLevel2Types(ctx)
	if err != nil {
		return nil, err
	}

	// Cache the result
	if s.cache != nil {
		s.cache.Set(cacheKey, types, 1)
	}

	return types, nil
}

// ClearARALevelCaches clears the cached ARA level data
// This can be called when ARA level data changes
func (s *Service) ClearARALevelCaches() {
	if s.cache != nil {
		s.cache.Del("ara_level1_categories")
		s.cache.Del("ara_level2_types")
	}
}

// ClassifyEquipmentByID classifies a specific equipment item by its ID
func (s *Service) ClassifyEquipmentByID(ctx context.Context, equipmentID string) (*models.ARAClassificationStats, error) {
	// Get OpenRouter API key from environment
	openRouterAPIKey := os.Getenv("OPENROUTER_API_KEY")
	if openRouterAPIKey == "" {
		return nil, fmt.Errorf("OPENROUTER_API_KEY environment variable is required")
	}

	// Get Firestore client
	firestoreClient, ok := s.db.GetFirestoreClient().(*firestore.Client)
	if !ok {
		return nil, fmt.Errorf("unable to get firestore client")
	}

	// Initialize services
	openRouterClient := openrouter.NewClient(openRouterAPIKey)
	araTaxonomyService := NewARATaxonomyService(firestoreClient)
	equipmentDataService := NewEquipmentDataService(firestoreClient)

	// Create default classification config
	config := models.DefaultARAClassificationConfig()
	config.BatchSize = 1 // Single equipment classification

	// Initialize ARA classification service
	araClassificationService := NewARAClassificationService(
		openRouterClient,
		araTaxonomyService,
		equipmentDataService,
		config,
	)

	// Classify the specific equipment
	stats, err := araClassificationService.ClassifyEquipmentByIDs(ctx, []string{equipmentID})
	if err != nil {
		return nil, fmt.Errorf("failed to classify equipment %s: %w", equipmentID, err)
	}

	// Clear admin dashboard cache since equipment data has changed
	s.ClearAdminDashboardStatsCache()

	return stats, nil
}
