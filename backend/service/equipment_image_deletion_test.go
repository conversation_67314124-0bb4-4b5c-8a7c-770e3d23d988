package service

import (
	"context"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/vima-inc/derental/storage"
)

// TestEquipmentImageDeletion tests the image deletion functionality for regular equipment only

// Mock storage for testing
type mockStorage struct {
	mock.Mock
}

func (m *mockStorage) Read(ctx context.Context, bucket string, path string) (io.ReadCloser, error) {
	args := m.Called(ctx, bucket, path)
	return args.Get(0).(io.ReadCloser), args.Error(1)
}

func (m *mockStorage) ReadWithMetadata(ctx context.Context, bucket string, path string) (io.ReadCloser, storage.Metadata, error) {
	args := m.Called(ctx, bucket, path)
	return args.Get(0).(io.ReadCloser), args.Get(1).(storage.Metadata), args.Error(2)
}

func (m *mockStorage) Write(ctx context.Context, bucket string, path string, data io.Reader) (string, error) {
	args := m.Called(ctx, bucket, path, data)
	return args.String(0), args.Error(1)
}

func (m *mockStorage) SignedURL(ctx context.Context, bucket string, path string) (string, error) {
	args := m.Called(ctx, bucket, path)
	return args.String(0), args.Error(1)
}

func (m *mockStorage) Delete(ctx context.Context, bucket string, path string) error {
	args := m.Called(ctx, bucket, path)
	return args.Error(0)
}

func (m *mockStorage) List(ctx context.Context, bucket string, prefix string) ([]string, error) {
	args := m.Called(ctx, bucket, prefix)
	return args.Get(0).([]string), args.Error(1)
}

func (m *mockStorage) DefaultBucket() string {
	args := m.Called()
	return args.String(0)
}

func TestExtractImagePathFromURL(t *testing.T) {
	service := &Service{}

	tests := []struct {
		name        string
		imageURL    string
		expected    string
		expectError bool
	}{
		{
			name:        "Valid Google Cloud Storage URL",
			imageURL:    "https://storage.googleapis.com/my-bucket/equipment_library/123.jpg",
			expected:    "equipment_library/123.jpg",
			expectError: false,
		},
		{
			name:        "Valid Google Cloud Storage URL with subdirectories",
			imageURL:    "https://storage.googleapis.com/my-bucket/equipment_library/subfolder/456.png",
			expected:    "equipment_library/subfolder/456.png",
			expectError: false,
		},
		{
			name:        "Valid Google Cloud Storage MediaLink URL",
			imageURL:    "https://storage.googleapis.com/download/storage/v1/b/dev-derental.appspot.com/o/equipment_library%2FdbnWJr1HyQhFCsrbEzlLogqGJeF3%2FdIEM2yNpbw0oI8ueBqm5.jpg?generation=1752161492350912&alt=media",
			expected:    "equipment_library/dbnWJr1HyQhFCsrbEzlLogqGJeF3/dIEM2yNpbw0oI8ueBqm5.jpg",
			expectError: false,
		},
		{
			name:        "Empty URL",
			imageURL:    "",
			expected:    "",
			expectError: false,
		},
		{
			name:        "Invalid URL format",
			imageURL:    "not-a-valid-url",
			expected:    "",
			expectError: true,
		},
		{
			name:        "URL with no path",
			imageURL:    "https://storage.googleapis.com/",
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.extractImagePathFromURL(tt.imageURL)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestDeleteExistingEquipmentImages(t *testing.T) {
	ctx := context.Background()

	t.Run("Delete existing images successfully", func(t *testing.T) {
		mockStorage := &mockStorage{}
		service := &Service{
			storage: mockStorage,
		}

		sanitizedName := "excavator"
		bucket := "test-bucket"
		prefix := "equipment_library/excavator."
		existingFiles := []string{
			"equipment_library/excavator.jpg",
			"equipment_library/excavator.png",
		}

		mockStorage.On("DefaultBucket").Return(bucket)
		mockStorage.On("List", ctx, bucket, prefix).Return(existingFiles, nil)
		mockStorage.On("Delete", ctx, bucket, "equipment_library/excavator.jpg").Return(nil)
		mockStorage.On("Delete", ctx, bucket, "equipment_library/excavator.png").Return(nil)

		err := service.deleteExistingEquipmentImages(ctx, sanitizedName)

		assert.NoError(t, err)
		mockStorage.AssertExpectations(t)
	})

	t.Run("Handle list error gracefully", func(t *testing.T) {
		mockStorage := &mockStorage{}
		service := &Service{
			storage: mockStorage,
		}

		sanitizedName := "excavator"
		bucket := "test-bucket"
		prefix := "equipment_library/excavator."

		mockStorage.On("DefaultBucket").Return(bucket)
		mockStorage.On("List", ctx, bucket, prefix).Return([]string{}, assert.AnError)

		err := service.deleteExistingEquipmentImages(ctx, sanitizedName)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "error listing existing images")
		mockStorage.AssertExpectations(t)
	})

	t.Run("Continue deleting even if one file fails", func(t *testing.T) {
		mockStorage := &mockStorage{}
		service := &Service{
			storage: mockStorage,
		}

		sanitizedName := "excavator"
		bucket := "test-bucket"
		prefix := "equipment_library/excavator."
		existingFiles := []string{
			"equipment_library/excavator.jpg",
			"equipment_library/excavator.png",
		}

		mockStorage.On("DefaultBucket").Return(bucket)
		mockStorage.On("List", ctx, bucket, prefix).Return(existingFiles, nil)
		mockStorage.On("Delete", ctx, bucket, "equipment_library/excavator.jpg").Return(assert.AnError)
		mockStorage.On("Delete", ctx, bucket, "equipment_library/excavator.png").Return(nil)

		err := service.deleteExistingEquipmentImages(ctx, sanitizedName)

		assert.NoError(t, err) // Should not fail even if individual deletes fail
		mockStorage.AssertExpectations(t)
	})
}

func TestSanitizeEquipmentName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Normal equipment name",
			input:    "Excavator Heavy Duty",
			expected: "excavatorheavyduty",
		},
		{
			name:     "Name with special characters",
			input:    "Concrete Mixer - 2000",
			expected: "concretemixer2000",
		},
		{
			name:     "Name with numbers and spaces",
			input:    "Crane 500 Ton",
			expected: "crane500ton",
		},
		{
			name:     "Empty name",
			input:    "",
			expected: "equipment",
		},
		{
			name:     "Only special characters",
			input:    "!@#$%^&*()",
			expected: "equipment",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := sanitizeEquipmentName(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDeleteEquipmentImage(t *testing.T) {
	ctx := context.Background()
	mockStorage := &mockStorage{}

	service := &Service{
		storage: mockStorage,
	}

	t.Run("Delete image successfully", func(t *testing.T) {
		imageURL := "https://storage.googleapis.com/my-bucket/equipment_library/123.jpg"

		mockStorage.On("DefaultBucket").Return("my-bucket")
		mockStorage.On("Delete", ctx, "my-bucket", "equipment_library/123.jpg").Return(nil)

		err := service.deleteEquipmentImage(ctx, imageURL)

		assert.NoError(t, err)
		mockStorage.AssertExpectations(t)
	})

	t.Run("Empty image URL", func(t *testing.T) {
		err := service.deleteEquipmentImage(ctx, "")

		assert.NoError(t, err)
		// No storage calls should be made
	})

	t.Run("Invalid image URL", func(t *testing.T) {
		imageURL := "invalid-url"

		err := service.deleteEquipmentImage(ctx, imageURL)

		assert.Error(t, err)
		// No storage calls should be made for invalid URLs
	})
}
