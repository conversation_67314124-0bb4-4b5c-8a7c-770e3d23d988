package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/vima-inc/derental/models"
)

// sanitizeEquipmentName converts equipment name to lowercase single word format
func sanitizeEquipmentName(name string) string {
	// Convert to lowercase
	name = strings.ToLower(name)
	// Remove special characters and replace spaces/hyphens with nothing
	reg := regexp.MustCompile(`[^a-z0-9]`)
	name = reg.ReplaceAllString(name, "")
	// Ensure it's not empty, fallback to "equipment" if needed
	if name == "" {
		name = "equipment"
	}
	return name
}

// deleteExistingEquipmentImages deletes all existing images for an equipment with the given sanitized name
func (s *Service) deleteExistingEquipmentImages(ctx context.Context, sanitizedName string) error {
	bucket := s.storage.DefaultBucket()
	prefix := fmt.Sprintf("equipment_library/%s.", sanitizedName)

	// List all files that start with the equipment name followed by a dot (for extensions)
	files, err := s.storage.List(ctx, bucket, prefix)
	if err != nil {
		return fmt.Errorf("error listing existing images: %w", err)
	}

	// Delete each existing image
	for _, file := range files {
		err := s.storage.Delete(ctx, bucket, file)
		if err != nil {
			// Log the error but continue with other files
			fmt.Printf("Warning: failed to delete existing image %s: %v\n", file, err)
		}
	}

	return nil
}

func (s *Service) AddToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error {
	return s.db.AddToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
}

func (s *Service) UpdateToolerBidzEquipment(ctx context.Context, toolerBidzEquipmentInventory models.ToolerBidzEquipment) error {
	return s.db.UpdateToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
}

func (s *Service) DeleteToolerBidzEquipment(ctx context.Context, id string) error {
	return s.db.DeleteToolerBidzEquipment(ctx, id)
}

func (s *Service) GetAllToolerBidzEquipment(ctx context.Context) ([]models.ToolerBidzEquipment, error) {
	return s.db.GetAllToolerBidzEquipment(ctx)
}

func (s *Service) UploadPhotoToEquipmentLibrary(ctx context.Context, id string, fileName string, data io.Reader) error {
	path := fmt.Sprintf("equipment_library/%s%s", id, filepath.Ext(fileName))

	photoURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
	if err != nil {
		return fmt.Errorf("error uploading photo: %w", err)
	}

	toolerBidzEquipmentInventory, err := s.db.GetToolerBidzEquipmentByID(ctx, id)
	if err != nil {
		return fmt.Errorf("error getting Tooler Bidz Equipment Inventory: %w", err)
	}

	toolerBidzEquipmentInventory.ImageLink = photoURL

	err = s.db.UpdateToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
	if err != nil {
		return fmt.Errorf("error updating tooler bidz equipment inventory: %w", err)
	}

	return nil
}

// UploadAdminMasterEquipmentImage uploads an image for admin master equipment with duplicate prevention
func (s *Service) UploadAdminMasterEquipmentImage(ctx context.Context, id string, fileName string, data io.Reader) error {
	// Get equipment data first to access the English name
	toolerBidzEquipmentInventory, err := s.db.GetToolerBidzEquipmentByID(ctx, id)
	if err != nil {
		return fmt.Errorf("error getting Tooler Bidz Equipment Inventory: %w", err)
	}

	// Create filename using sanitized equipment name
	sanitizedName := sanitizeEquipmentName(toolerBidzEquipmentInventory.NameEN)

	// Delete existing images with the same name but different extensions
	err = s.deleteExistingEquipmentImages(ctx, sanitizedName)
	if err != nil {
		// Log the error but don't fail the upload - the old image might not exist
		fmt.Printf("Warning: failed to delete existing images for %s: %v\n", sanitizedName, err)
	}

	// Create new path with the new file extension
	path := fmt.Sprintf("equipment_library/%s%s", sanitizedName, filepath.Ext(fileName))

	photoURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
	if err != nil {
		return fmt.Errorf("error uploading photo: %w", err)
	}

	toolerBidzEquipmentInventory.ImageLink = photoURL

	err = s.db.UpdateToolerBidzEquipment(ctx, toolerBidzEquipmentInventory)
	if err != nil {
		return fmt.Errorf("error updating tooler bidz equipment inventory: %w", err)
	}

	return nil
}

func (s *Service) GetBidzEquipmentByID(ctx context.Context, id string) (models.ToolerBidzEquipment, error) {
	return s.db.GetToolerBidzEquipmentByID(ctx, id)
}

func (s *Service) DropToolerBidzEquipments(ctx context.Context) error {
	return s.db.DropToolerBidzEquipments(ctx)
}
