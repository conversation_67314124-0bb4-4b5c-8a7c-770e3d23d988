package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/db/mocks"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

func TestGetMasterEquipmentByID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Mock data
	mockEquipment := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Test Equipment",
		NameFR: "Équipement de Test",
		Brand:  "Test Brand",
		Model:  "Test Model",
	}

	// Setup mock expectations
	mockDB.On("GetToolerBidzEquipmentByID", mock.Anything, "test-id").Return(mockEquipment, nil)

	// Create request
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/admin/master-equipment/test-id", nil)
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Set admin email in context
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Execute
	handler := getMasterEquipmentByID(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response models.ToolerBidzEquipment
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, mockEquipment.ID, response.ID)
	assert.Equal(t, mockEquipment.NameEN, response.NameEN)

	mockDB.AssertExpectations(t)
}

func TestGetMasterEquipmentByID_Unauthorized(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Create request without admin email in context
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/admin/master-equipment/test-id", nil)
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Execute
	handler := getMasterEquipmentByID(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestUpdateMasterEquipment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Mock data
	updateData := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Updated Equipment",
		NameFR: "Équipement Mis à Jour",
		Brand:  "Updated Brand",
		Model:  "Updated Model",
	}

	existingEquipment := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "Original Equipment",
		NameFR: "Équipement Original",
	}

	// Setup mock expectations
	mockDB.On("GetToolerBidzEquipmentByID", mock.Anything, "test-id").Return(existingEquipment, nil)
	mockDB.On("UpdateToolerBidzEquipment", mock.Anything, mock.MatchedBy(func(eq models.ToolerBidzEquipment) bool {
		return eq.ID == "test-id" && eq.NameEN == "Updated Equipment"
	})).Return(nil)

	// Create request body
	jsonData, _ := json.Marshal(updateData)

	// Create request
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("PUT", "/admin/master-equipment/test-id", bytes.NewBuffer(jsonData))
	c.Request.Header.Set("Content-Type", "application/json")
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Set admin email in context
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Execute
	handler := updateMasterEquipment(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Master equipment updated successfully", response["message"])

	mockDB.AssertExpectations(t)
}

func TestUpdateMasterEquipment_ValidationError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup
	mockDB := &mocks.Database{}
	mockService := service.New(service.WithDB(mockDB))

	// Invalid data (missing required fields)
	updateData := models.ToolerBidzEquipment{
		ID:     "test-id",
		NameEN: "", // Empty required field
		NameFR: "", // Empty required field
	}

	// Create request body
	jsonData, _ := json.Marshal(updateData)

	// Create request
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("PUT", "/admin/master-equipment/test-id", bytes.NewBuffer(jsonData))
	c.Request.Header.Set("Content-Type", "application/json")
	c.Params = gin.Params{{Key: "id", Value: "test-id"}}

	// Set admin email in context
	ctx := context.WithValue(c.Request.Context(), middleware.EmailKey, "<EMAIL>")
	c.Request = c.Request.WithContext(ctx)

	// Execute
	handler := updateMasterEquipment(mockService)
	handler(c)

	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Validation failed", response["error"])
	assert.Contains(t, response, "errors")
}

// TestUploadMasterEquipmentImage is commented out because it requires complex storage mocking
// The functionality is tested through integration tests
// func TestUploadMasterEquipmentImage(t *testing.T) {
// 	// This test would require mocking storage operations which is complex
// 	// The upload functionality is covered by integration tests
// }

func TestValidateMasterEquipment(t *testing.T) {
	tests := []struct {
		name      string
		equipment models.ToolerBidzEquipment
		wantErrs  int
	}{
		{
			name: "Valid equipment",
			equipment: models.ToolerBidzEquipment{
				NameEN:    "Valid Name EN",
				NameFR:    "Valid Name FR",
				ImageLink: "https://example.com/image.jpg",
			},
			wantErrs: 0,
		},
		{
			name: "Missing required fields",
			equipment: models.ToolerBidzEquipment{
				NameEN: "",
				NameFR: "",
			},
			wantErrs: 2,
		},
		{
			name: "Invalid image URL",
			equipment: models.ToolerBidzEquipment{
				NameEN:    "Valid Name EN",
				NameFR:    "Valid Name FR",
				ImageLink: "invalid-url",
			},
			wantErrs: 1,
		},
		{
			name: "Negative ARA IDs",
			equipment: models.ToolerBidzEquipment{
				NameEN:      "Valid Name EN",
				NameFR:      "Valid Name FR",
				ARALevel1ID: -1,
				ARALevel2ID: -1,
			},
			wantErrs: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := validateMasterEquipment(tt.equipment)
			assert.Equal(t, tt.wantErrs, len(errors))
		})
	}
}
