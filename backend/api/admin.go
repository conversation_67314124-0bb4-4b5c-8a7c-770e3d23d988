package api

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

// adminSignin handles admin authentication with domain validation
func adminSignin(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}

		err := c.BindJSON(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.<PERSON>rror(err)
			return
		}

		// Validate domain before attempting authentication
		if !isValidAdminDomain(body.Email) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access restricted to @derentalequipment.com domain"})
			return
		}

		resp, err := svc.AdminSignIn(c.Request.Context(), body.Email, body.Password)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// getMasterInventory returns all master inventory items for admin dashboard
func getMasterInventory(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		masterInventory, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  masterInventory,
			"total": len(masterInventory),
		})
	}
}

// getAdminDashboardStats returns dashboard statistics for admin
func getAdminDashboardStats(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		stats, err := svc.GetAdminDashboardStats(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, stats)
	}
}

// isValidAdminDomain checks if email has the required domain
func isValidAdminDomain(email string) bool {
	return len(email) > 22 && email[len(email)-22:] == "@derentalequipment.com"
}

// getARALevel1Categories returns all ARA Level 1 categories for admin filters
func getARALevel1Categories(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		categories, err := svc.GetAllARALevel1Categories(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  categories,
			"total": len(categories),
		})
	}
}

// getARALevel2Types returns all ARA Level 2 types for admin filters
func getARALevel2Types(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		types, err := svc.GetAllARALevel2Types(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  types,
			"total": len(types),
		})
	}
}

// getMasterEquipmentByID returns a single master equipment item by ID for admin editing
func getMasterEquipmentByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		equipment, err := svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, equipment)
	}
}

// validateMasterEquipment validates the master equipment data
func validateMasterEquipment(equipment models.ToolerBidzEquipment) []string {
	var errors []string

	// Required fields validation
	if strings.TrimSpace(equipment.NameEN) == "" {
		errors = append(errors, "English name is required")
	}
	if strings.TrimSpace(equipment.NameFR) == "" {
		errors = append(errors, "French name is required")
	}

	// URL validation for image link
	if equipment.ImageLink != "" && !strings.HasPrefix(equipment.ImageLink, "http") {
		errors = append(errors, "Image link must be a valid URL")
	}

	// ARA classification validation
	if equipment.ARALevel1ID < 0 {
		errors = append(errors, "ARA Level 1 ID must be non-negative")
	}
	if equipment.ARALevel2ID < 0 {
		errors = append(errors, "ARA Level 2 ID must be non-negative")
	}

	return errors
}

// updateMasterEquipment updates a master equipment item for admin
func updateMasterEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		var equipment models.ToolerBidzEquipment
		err := c.BindJSON(&equipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			_ = c.Error(err)
			return
		}

		// Ensure the ID matches the URL parameter
		equipment.ID = equipmentID

		// Validate the equipment data
		validationErrors := validateMasterEquipment(equipment)
		if len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Validation failed",
				"errors": validationErrors,
			})
			return
		}

		// Check if equipment exists before updating
		_, err = svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Equipment not found"})
			_ = c.Error(err)
			return
		}

		err = svc.UpdateToolerBidzEquipment(c.Request.Context(), equipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update equipment: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Master equipment updated successfully"})
	}
}

// uploadMasterEquipmentImage uploads an image for master equipment
func uploadMasterEquipmentImage(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File upload failed: " + err.Error()})
			_ = c.Error(err)
			return
		}
		defer file.Close()

		// Validate file type
		allowedTypes := map[string]bool{
			"image/jpeg": true,
			"image/jpg":  true,
			"image/png":  true,
			"image/gif":  true,
			"image/webp": true,
		}

		contentType := header.Header.Get("Content-Type")
		if !allowedTypes[contentType] {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed"})
			return
		}

		// Validate file size (max 10MB)
		const maxFileSize = 10 * 1024 * 1024 // 10MB
		if header.Size > maxFileSize {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File size too large. Maximum size is 10MB"})
			return
		}

		// Check if equipment exists
		_, err = svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Equipment not found"})
			_ = c.Error(err)
			return
		}

		err = svc.UploadAdminMasterEquipmentImage(c.Request.Context(), equipmentID, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload image: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Image uploaded successfully"})
	}
}
