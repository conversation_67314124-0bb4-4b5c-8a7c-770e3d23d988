package storage

import (
	"context"
	"io"
)

// Storage is a interface representing a storage.
type Storage interface {
	// Read reads a file from storage.
	Read(ctx context.Context, bucket string, path string) (io.ReadCloser, error)
	// ReadWithMetadata reads a file from google storage with metadata.
	ReadWithMetadata(ctx context.Context, bucket string, path string) (io.ReadCloser, Metadata, error)
	// Write writes a file to firebase storage.
	Write(ctx context.Context, bucket string, path string, data io.Reader) (string, error)
	// SignedURL returns a signed url for a file in storage.
	SignedURL(ctx context.Context, bucket string, path string) (string, error)
	// Delete deletes a file from storage.
	Delete(ctx context.Context, bucket string, path string) error
	// List lists files with a given prefix.
	List(ctx context.Context, bucket string, prefix string) ([]string, error)
	// DefaultBucket returns the default bucket.
	DefaultBucket() string
}

type Metadata map[string]string
