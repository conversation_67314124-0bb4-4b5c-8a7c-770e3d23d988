import { Component } from 'react';
import NotFoundPage from './features/not_found/Not_found_page';
import SomethingWentWrong from './style/assets/img/something_went_wrong.png';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  componentDidCatch() {
    this.setState({ hasError: true });
  }

  render() {
    if (this.state.hasError) {
      return (
        <NotFoundPage
          t={this.props.t}
          text="Something_went_wrong"
          description="Something_went_wrong_description"
          img={SomethingWentWrong}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
