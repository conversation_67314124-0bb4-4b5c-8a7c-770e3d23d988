import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Col, 
  Card, 
  <PERSON>, 
  Alert, 
  Spinner
} from 'react-bootstrap';
import { MdSave as SaveIcon, MdCancel as CancelIcon, MdUpload as UploadIcon } from 'react-icons/md';
import axios from 'axios';
import { getCookies } from '../../shared/helpers/Cookies';

const validationSchema = Yup.object().shape({
  name_en: Yup.string()
    .trim()
    .required('English name is required')
    .min(2, 'English name must be at least 2 characters')
    .max(255, 'English name must be less than 255 characters'),
  name_fr: Yup.string()
    .trim()
    .required('French name is required')
    .min(2, 'French name must be at least 2 characters')
    .max(255, 'French name must be less than 255 characters'),
  description: Yup.string()
    .max(1000, 'Description must be less than 1000 characters'),
  description_en: Yup.string()
    .max(1000, 'English description must be less than 1000 characters'),
  description_fr: Yup.string()
    .max(1000, 'French description must be less than 1000 characters'),
  brand: Yup.string()
    .max(100, 'Brand must be less than 100 characters'),
  model: Yup.string()
    .max(100, 'Model must be less than 100 characters'),
  category: Yup.array().of(Yup.string()),
  sub_category: Yup.array().of(Yup.string()),
  alias: Yup.object().shape({
    en: Yup.array().of(Yup.string()),
    fr: Yup.array().of(Yup.string())
  }),
  image_link: Yup.string()
    .url('Must be a valid URL')
    .nullable(),
  ara_level1_id: Yup.number()
    .min(0, 'ARA Level 1 ID must be a positive number'),
  ara_level2_id: Yup.number()
    .min(0, 'ARA Level 2 ID must be a positive number'),
  ara_level1_name: Yup.string()
    .max(100, 'ARA Level 1 name must be less than 100 characters'),
  ara_level2_name: Yup.string()
    .max(100, 'ARA Level 2 name must be less than 100 characters')
});

function MasterEquipmentUpdateModern() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [equipment, setEquipment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [saving, setSaving] = useState(false);
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadingImage, setUploadingImage] = useState(false);

  const baseURL = import.meta.env.VITE_REACT_APP_BASE_URL;

  const initialValues = equipment ? {
    name_en: equipment.name_en || '',
    name_fr: equipment.name_fr || '',
    brand: equipment.brand || '',
    model: equipment.model || '',
    image_link: equipment.image_link || '',
    description: equipment.description || '',
    description_en: equipment.description_en || '',
    description_fr: equipment.description_fr || '',
    category: equipment.category || [],
    sub_category: equipment.sub_category || [],
    alias: {
      en: equipment.alias?.en || [],
      fr: equipment.alias?.fr || []
    },
    ara_level1_id: equipment.ara_level1_id || 0,
    ara_level2_id: equipment.ara_level2_id || 0,
    ara_level1_name: equipment.ara_level1_name || '',
    ara_level2_name: equipment.ara_level2_name || ''
  } : {
    name_en: '',
    name_fr: '',
    brand: '',
    model: '',
    image_link: '',
    description: '',
    description_en: '',
    description_fr: '',
    category: [],
    sub_category: [],
    alias: { en: [], fr: [] },
    ara_level1_id: 0,
    ara_level2_id: 0,
    ara_level1_name: '',
    ara_level2_name: ''
  };

  useEffect(() => {
    fetchEquipment();
  }, [id]);

  const fetchEquipment = async () => {
    try {
      setLoading(true);
      setError(''); // Clear previous errors
      const token = getCookies('adminToken');
      if (!token) {
        setError('Authentication required. Redirecting to login...');
        setTimeout(() => {
          navigate('/admin/login');
        }, 2000);
        return;
      }
      const response = await axios.get(`${baseURL}/admin/master-equipment/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      setEquipment(response.data);
    } catch (err) {
      if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.');
      } else if (err.response?.status === 403) {
        setError('Access denied. Admin privileges required.');
      } else if (err.response?.status === 404) {
        setError(`Equipment with ID "${id}" not found.`);
      } else if (err.response?.status >= 500) {
        setError('Server error. Please try again later.');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network error. Please check your connection and ensure the backend server is running.');
      } else {
        setError(`Failed to load equipment data: ${err.response?.data?.error || err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values) => {
    try {
      setSaving(true);
      setError('');

      const token = getCookies('adminToken');
      if (!token) {
        setError('Authentication required. Please log in again.');
        return;
      }

      await axios.put(`${baseURL}/admin/master-equipment/${id}`, values, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Master equipment updated successfully!');
      setTimeout(() => {
        navigate('/admin/dashboard');
      }, 2000);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to update equipment');
    } finally {
      setSaving(false);
    }
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setError('Image file size must be less than 5MB');
        return;
      }
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return;

    try {
      setUploadingImage(true);
      const formData = new FormData();
      formData.append('file', imageFile);

      const token = getCookies('adminToken');
      const response = await axios.post(`${baseURL}/admin/master-equipment/${id}/image`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      setEquipment(prev => ({ ...prev, image_link: response.data.image_url }));
      setImageFile(null);
      setImagePreview(null);
      setSuccess('Image uploaded successfully!');
    } catch (err) {
      setError('Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" variant="primary" />
      </Container>
    );
  }

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className="w-100" style={{ backgroundColor: '#061C3D', color: 'white', padding: '1rem 1.5rem' }}>
        <Container>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              {/* Derental Logo */}
              <div className="me-4">
                <span style={{ fontSize: '1.5rem', fontWeight: 600 }}>
                  <span
                    style={{
                      backgroundColor: '#ECA869',
                      padding: '2px 2px 1px 6px',
                      borderRadius: '0px 13px',
                      color: 'black'
                    }}
                  >
                    De
                  </span>
                  rental
                </span>
              </div>
              <h4 className="mb-0" style={{ fontWeight: '600' }}>Update Master Equipment</h4>
            </div>
            <div className="d-flex align-items-center">
              <div className="me-3">
                <div style={{ fontSize: '0.9rem', opacity: 0.9 }}>
                  Logged in as:
                </div>
                <div style={{ fontWeight: '500' }}><EMAIL></div>
              </div>
              <Button 
                variant="outline-light" 
                size="sm"
                onClick={() => navigate('/admin/dashboard')}
                style={{ fontWeight: '500' }}
              >
                Back to Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </div>

      <Container className="mt-4">
        {error && (
          <Alert variant="danger" className="mb-4">
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" className="mb-4">
            {success}
          </Alert>
        )}

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue, isSubmitting, handleSubmit: formikHandleSubmit, submitForm }) => (
            <Form onSubmit={formikHandleSubmit}>
              <Row>
                {/* Basic Information */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '1.5rem' }}>
                      <h5 style={{ 
                        fontWeight: '600', 
                        color: '#495057', 
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        📝 Basic Information
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            English Name *
                          </label>
                          <input
                            type="text"
                            className={`form-control ${touched.name_en && errors.name_en ? 'is-invalid' : ''}`}
                            name="name_en"
                            value={values.name_en}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                          {touched.name_en && errors.name_en && (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {errors.name_en}
                            </div>
                          )}
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            French Name *
                          </label>
                          <input
                            type="text"
                            className={`form-control ${touched.name_fr && errors.name_fr ? 'is-invalid' : ''}`}
                            name="name_fr"
                            value={values.name_fr}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                          {touched.name_fr && errors.name_fr && (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {errors.name_fr}
                            </div>
                          )}
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Brand
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="brand"
                            value={values.brand}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Model
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="model"
                            value={values.model}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col xs={12} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Image URL
                          </label>
                          <input
                            type="text"
                            className={`form-control ${touched.image_link && errors.image_link ? 'is-invalid' : ''}`}
                            name="image_link"
                            value={values.image_link}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                          {touched.image_link && errors.image_link && (
                            <div className="invalid-feedback d-block" style={{ fontSize: '0.8rem' }}>
                              {errors.image_link}
                            </div>
                          )}
                        </Col>

                        {/* Image Upload Section */}
                        <Col xs={12} className="mb-3">
                          <h6 style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '1rem',
                            borderBottom: '1px solid #e9ecef',
                            paddingBottom: '0.5rem'
                          }}>
                            📷 Upload New Image
                          </h6>
                          <div className="d-flex align-items-center gap-3 mb-3">
                            <input
                              accept="image/*"
                              style={{ display: 'none' }}
                              id="image-upload"
                              type="file"
                              onChange={handleImageChange}
                            />
                            <label
                              htmlFor="image-upload"
                              style={{
                                display: 'inline-block',
                                backgroundColor: 'transparent',
                                color: '#007bff',
                                border: '2px solid #007bff',
                                borderRadius: '8px',
                                fontWeight: '500',
                                padding: '8px 16px',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease-in-out',
                                textDecoration: 'none'
                              }}
                              onMouseOver={(e) => {
                                e.target.style.backgroundColor = '#007bff';
                                e.target.style.color = 'white';
                              }}
                              onMouseOut={(e) => {
                                e.target.style.backgroundColor = 'transparent';
                                e.target.style.color = '#007bff';
                              }}
                              onFocus={(e) => {
                                e.target.style.backgroundColor = '#007bff';
                                e.target.style.color = 'white';
                              }}
                              onBlur={(e) => {
                                e.target.style.backgroundColor = 'transparent';
                                e.target.style.color = '#007bff';
                              }}
                            >
                              <UploadIcon style={{ marginRight: '8px' }} />
                              Choose Image
                            </label>
                            {imageFile && (
                              <Button
                                variant="success"
                                onClick={uploadImage}
                                disabled={uploadingImage}
                                style={{
                                  borderRadius: '8px',
                                  fontWeight: '500',
                                  padding: '8px 16px'
                                }}
                              >
                                {uploadingImage ? (
                                  <>
                                    <Spinner size="sm" className="me-2" />
                                    Uploading...
                                  </>
                                ) : (
                                  'Upload'
                                )}
                              </Button>
                            )}
                          </div>

                          {/* Image Preview */}
                          {(imagePreview || values.image_link) && (
                            <div className="d-flex gap-3 align-items-start">
                              {imagePreview && (
                                <div>
                                  <small className="text-muted d-block mb-2" style={{ fontWeight: '500' }}>
                                    New Image Preview:
                                  </small>
                                  <img
                                    src={imagePreview}
                                    alt="Preview"
                                    style={{
                                      width: '100px',
                                      height: '100px',
                                      objectFit: 'cover',
                                      borderRadius: '8px',
                                      border: '2px solid #28a745',
                                      boxShadow: '0 2px 8px rgba(40, 167, 69, 0.2)'
                                    }}
                                  />
                                </div>
                              )}
                              {values.image_link && (
                                <div>
                                  <small className="text-muted d-block mb-2" style={{ fontWeight: '500' }}>
                                    Current Image:
                                  </small>
                                  <img
                                    src={values.image_link}
                                    alt="Current"
                                    style={{
                                      width: '100px',
                                      height: '100px',
                                      objectFit: 'cover',
                                      borderRadius: '8px',
                                      border: '2px solid #e9ecef',
                                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                    }}
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Descriptions */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '1.5rem' }}>
                      <h5 style={{
                        fontWeight: '600',
                        color: '#495057',
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        📝 Descriptions
                      </h5>
                      <Row>
                        <Col xs={12} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            General Description
                          </label>
                          <textarea
                            className="form-control"
                            rows={3}
                            name="description"
                            value={values.description}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                              resize: 'vertical'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            English Description
                          </label>
                          <textarea
                            className="form-control"
                            rows={3}
                            name="description_en"
                            value={values.description_en}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                              resize: 'vertical'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            French Description
                          </label>
                          <textarea
                            className="form-control"
                            rows={3}
                            name="description_fr"
                            value={values.description_fr}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                              resize: 'vertical'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Categories */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '1.5rem' }}>
                      <h5 style={{
                        fontWeight: '600',
                        color: '#495057',
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        📂 Categories
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Categories
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="category"
                            value={Array.isArray(values.category) ? values.category.join(', ') : values.category || ''}
                            onChange={(e) => {
                              const categories = e.target.value.split(',').map(cat => cat.trim()).filter(cat => cat);
                              setFieldValue('category', categories);
                            }}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            placeholder="Enter categories separated by commas"
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            Sub Categories
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="sub_category"
                            value={Array.isArray(values.sub_category) ? values.sub_category.join(', ') : values.sub_category || ''}
                            onChange={(e) => {
                              const subCategories = e.target.value.split(',').map(cat => cat.trim()).filter(cat => cat);
                              setFieldValue('sub_category', subCategories);
                            }}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            placeholder="Enter sub categories separated by commas"
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Aliases */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '1.5rem' }}>
                      <h5 style={{
                        fontWeight: '600',
                        color: '#495057',
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        🏷️ Aliases
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            English Aliases
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            value={Array.isArray(values.alias?.en) ? values.alias.en.join(', ') : ''}
                            onChange={(e) => {
                              const aliases = e.target.value.split(',').map(alias => alias.trim()).filter(alias => alias);
                              setFieldValue('alias.en', aliases);
                            }}
                            placeholder="Enter English aliases separated by commas"
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            French Aliases
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            value={Array.isArray(values.alias?.fr) ? values.alias.fr.join(', ') : ''}
                            onChange={(e) => {
                              const aliases = e.target.value.split(',').map(alias => alias.trim()).filter(alias => alias);
                              setFieldValue('alias.fr', aliases);
                            }}
                            placeholder="Enter French aliases separated by commas"
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* ARA Classification */}
                <Col xs={12}>
                  <Card style={{
                    border: 'none',
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    marginBottom: '1.5rem'
                  }}>
                    <Card.Body style={{ padding: '1.5rem' }}>
                      <h5 style={{
                        fontWeight: '600',
                        color: '#495057',
                        marginBottom: '1.5rem',
                        borderBottom: '2px solid #ECA869',
                        paddingBottom: '0.5rem'
                      }}>
                        🏗️ ARA Classification
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            ARA Level 1 Category
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="ara_level1_name"
                            value={values.ara_level1_name}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className="form-label" style={{
                            fontWeight: '600',
                            color: '#495057',
                            marginBottom: '8px',
                            fontSize: '0.9rem'
                          }}>
                            ARA Level 2 Type
                          </label>
                          <input
                            type="text"
                            className="form-control"
                            name="ara_level2_name"
                            value={values.ara_level2_name}
                            onChange={handleChange}
                            onBlur={(e) => {
                              handleBlur(e);
                              e.target.style.borderColor = '#e9ecef';
                              e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                            }}
                            style={{
                              padding: '12px 16px',
                              border: '2px solid #e9ecef',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease-in-out',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }}
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Action Buttons */}
                <Col xs={12}>
                  <div className="d-flex justify-content-end gap-3 mt-4">
                    <Button
                      variant="outline-secondary"
                      onClick={() => navigate('/admin/dashboard')}
                      disabled={isSubmitting || saving}
                      style={{
                        borderRadius: '8px',
                        fontWeight: '500',
                        padding: '12px 24px',
                        border: '2px solid #6c757d',
                        transition: 'all 0.2s ease-in-out'
                      }}
                    >
                      <CancelIcon className="me-2" />
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={isSubmitting || saving}
                      onClick={(e) => {
                        e.preventDefault();
                        submitForm();
                      }}
                      style={{
                        borderRadius: '8px',
                        fontWeight: '500',
                        padding: '12px 24px',
                        backgroundColor: '#ECA869',
                        borderColor: '#ECA869',
                        color: 'black',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseOver={(e) => {
                        e.target.style.backgroundColor = '#d4956b';
                        e.target.style.borderColor = '#d4956b';
                      }}
                      onMouseOut={(e) => {
                        e.target.style.backgroundColor = '#ECA869';
                        e.target.style.borderColor = '#ECA869';
                      }}
                    >
                      {saving ? (
                        <>
                          <Spinner size="sm" className="me-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <SaveIcon className="me-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          )}
        </Formik>
      </Container>
    </Container>
  );
}

export default MasterEquipmentUpdateModern;
