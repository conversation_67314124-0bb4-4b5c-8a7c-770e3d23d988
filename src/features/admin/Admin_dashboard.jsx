import React, { useState, useEffect, useMemo } from 'react';
import { Container, Row, Col, Card, Button, Alert, Table, Dropdown, Modal } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { getCookies, clearCookies } from '../../shared/helpers/Cookies';
import ClassificationModal from './Classification_modal';

function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({});
  const [masterInventory, setMasterInventory] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);

  // Filter states (display only, no functionality)
  const [selectedARALevel1, setSelectedARALevel1] = useState('');
  const [selectedARALevel2, setSelectedARALevel2] = useState('');

  // Filter data
  const [araLevel1Categories, setAraLevel1Categories] = useState([]);
  const [araLevel2Types, setAraLevel2Types] = useState([]);

  // Search states for dropdowns
  const [level1Search, setLevel1Search] = useState('');
  const [level2Search, setLevel2Search] = useState('');
  const [showLevel1Dropdown, setShowLevel1Dropdown] = useState(false);
  const [showLevel2Dropdown, setShowLevel2Dropdown] = useState(false);

  // Main search state
  const [searchTerm, setSearchTerm] = useState('');

  // Image modal state
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState({ src: '', alt: '', title: '' });

  // Classification modal states
  const [showClassificationModal, setShowClassificationModal] = useState(false);
  const [selectedEquipmentForClassification, setSelectedEquipmentForClassification] = useState(null);

  const navigate = useNavigate();

  // Get admin email from cookies
  const adminEmail = getCookies('email');

  // Handle image click to show modal
  const handleImageClick = (imageSrc, equipmentName, equipmentId) => {
    setSelectedImage({
      src: imageSrc,
      alt: equipmentName || 'Equipment',
      title: `${equipmentName || 'Equipment'} (ID: ${equipmentId})`
    });
    setShowImageModal(true);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowImageModal(false);
    setSelectedImage({ src: '', alt: '', title: '' });
  };

  // Handle classification modal
  const handleClassifyClick = (equipment) => {
    setSelectedEquipmentForClassification(equipment);
    setShowClassificationModal(true);
  };

  const handleCloseClassificationModal = () => {
    setShowClassificationModal(false);
    setSelectedEquipmentForClassification(null);
  };

  const handleClassificationSuccess = () => {
    // Refresh the master inventory data
    fetchDashboardData();
  };

  // Filter dropdown data
  const filteredLevel1Categories = useMemo(() => {
    return araLevel1Categories.filter(category =>
      (category.name || category.category_name || '').toLowerCase().includes(level1Search.toLowerCase())
    );
  }, [araLevel1Categories, level1Search]);

  const filteredLevel2Types = useMemo(() => {
    let filtered = araLevel2Types;

    // First filter by selected Level 1 category if one is selected
    if (selectedARALevel1) {
      filtered = filtered.filter(type => type.level1_id === selectedARALevel1);
    }

    // Then filter by search term
    filtered = filtered.filter(type =>
      (type.name || type.type_name || '').toLowerCase().includes(level2Search.toLowerCase())
    );

    return filtered; // Show all filtered items
  }, [araLevel2Types, level2Search, selectedARALevel1]);

  // Filter the master inventory based on search term and selected ARA levels
  const filteredInventory = useMemo(() => {
    let filtered = masterInventory;

    // Filter by search term (ID and Name)
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(item => {
        const id = (item.id || '').toString().toLowerCase();
        const nameEn = (item.name_en || '').toLowerCase();
        const nameFr = (item.name_fr || '').toLowerCase();

        return id.includes(searchLower) ||
               nameEn.includes(searchLower) ||
               nameFr.includes(searchLower);
      });
    }

    // Filter by ARA Level 1 if selected
    if (selectedARALevel1) {
      const selectedLevel1Name = araLevel1Categories.find(cat => cat.id === selectedARALevel1)?.name ||
                                 araLevel1Categories.find(cat => cat.id === selectedARALevel1)?.category_name;
      if (selectedLevel1Name) {
        filtered = filtered.filter(item =>
          item.ara_level1_name && item.ara_level1_name.toLowerCase() === selectedLevel1Name.toLowerCase()
        );
      }
    }

    // Filter by ARA Level 2 if selected
    if (selectedARALevel2) {
      const selectedLevel2Name = araLevel2Types.find(type => type.id === selectedARALevel2)?.name ||
                                 araLevel2Types.find(type => type.id === selectedARALevel2)?.type_name;
      if (selectedLevel2Name) {
        filtered = filtered.filter(item =>
          item.ara_level2_name && item.ara_level2_name.toLowerCase() === selectedLevel2Name.toLowerCase()
        );
      }
    }

    return filtered;
  }, [masterInventory, searchTerm, selectedARALevel1, selectedARALevel2, araLevel1Categories, araLevel2Types]);

  // Pagination logic (now using filtered data)
  const { paginatedData, totalPages } = useMemo(() => {
    const startIdx = (currentPage - 1) * itemsPerPage;
    const endIdx = startIdx + itemsPerPage;
    const paginatedItems = filteredInventory.slice(startIdx, endIdx);
    const totalPgs = Math.ceil(filteredInventory.length / itemsPerPage);

    return {
      paginatedData: paginatedItems,
      totalPages: totalPgs
    };
  }, [filteredInventory, currentPage, itemsPerPage]);

  // Reset to page 1 when filters or search term change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedARALevel1, selectedARALevel2, searchTerm]);

  // Clear Level 2 selection when Level 1 changes
  useEffect(() => {
    if (selectedARALevel1) {
      // Check if current Level 2 selection is still valid for the new Level 1
      const currentLevel2Valid = araLevel2Types.some(type =>
        type.id === selectedARALevel2 && type.level1_id === selectedARALevel1
      );
      if (!currentLevel2Valid) {
        setSelectedARALevel2('');
      }
    }
  }, [selectedARALevel1, araLevel2Types, selectedARALevel2]);

  const handleLogout = () => {
    clearCookies('adminToken');
    navigate('/admin/login');
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const token = getCookies('adminToken');
      const config = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      };

      const baseURL = import.meta.env.VITE_REACT_APP_BASE_URL;
      const [statsResponse, inventoryResponse] = await Promise.all([
        axios.get(`${baseURL}/admin/dashboard/stats`, config),
        axios.get(`${baseURL}/admin/master-inventory`, config)
      ]);

      setStats(statsResponse.data);
      setMasterInventory(inventoryResponse.data.data || []);
    } catch (err) {
      if (err.response?.status === 401 || err.response?.status === 403) {
        handleLogout();
      } else {
        setError('Failed to load dashboard data');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchFilterData = async () => {
    try {
      const token = getCookies('adminToken');
      const config = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      };

      const baseURL = import.meta.env.VITE_REACT_APP_BASE_URL;
      const [level1Response, level2Response] = await Promise.all([
        axios.get(`${baseURL}/admin/ara-level1-categories`, config),
        axios.get(`${baseURL}/admin/ara-level2-types`, config)
      ]);

      setAraLevel1Categories(level1Response.data.data || []);
      setAraLevel2Types(level2Response.data.data || []);
    } catch (err) {
      setError('Failed to load filter data');
    }
  };

  useEffect(() => {
    fetchDashboardData();
    fetchFilterData();
  }, []);

  if (loading) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className="w-100" style={{ backgroundColor: '#061C3D', color: 'white', padding: '1rem 1.5rem' }}>
        <Container>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              {/* Derental Logo */}
              <div className="me-4">
                <span style={{ fontSize: '1.5rem', fontWeight: 600 }}>
                  <span
                    style={{
                      backgroundColor: '#ECA869',
                      padding: '2px 2px 1px 6px',
                      borderRadius: '0px 13px',
                      color: 'black'
                    }}
                  >
                    De
                  </span>
                  rental
                </span>
              </div>
              {/* Admin Dashboard Title */}
              <h4 className="mb-0">Admin Dashboard</h4>
            </div>

            {/* Admin Info and Logout */}
            <div className="d-flex align-items-center">
              <div className="me-3 text-end">
                <small className="d-block opacity-75">Logged in as:</small>
                <span className="fw-bold">{adminEmail || 'Admin User'}</span>
              </div>
              <Button
                variant="outline-light"
                size="sm"
                onClick={handleLogout}
                className="ms-2"
              >
                Logout
              </Button>
            </div>
          </div>
        </Container>
      </div>

      <Container className="mt-4">
        {/* Stats Cards */}
        <Row className="mb-4">
        <Col md={3}>
          <Card style={{
            border: 'none',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            cursor: 'default'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.12)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
          }}>
            <Card.Body style={{ padding: '1.5rem', textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                marginBottom: '0.5rem',
                opacity: 0.9
              }}>
                📦
              </div>
              <h3 style={{
                fontSize: '2rem',
                fontWeight: '700',
                margin: '0.5rem 0',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                {stats.total_master_inventory || 0}
              </h3>
              <p style={{
                margin: 0,
                fontSize: '0.9rem',
                fontWeight: '500',
                opacity: 0.9
              }}>
                Master Inventory
              </p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card style={{
            border: 'none',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            color: 'white',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            cursor: 'default'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.12)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
          }}>
            <Card.Body style={{ padding: '1.5rem', textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                marginBottom: '0.5rem',
                opacity: 0.9
              }}>
                👥
              </div>
              <h3 style={{
                fontSize: '2rem',
                fontWeight: '700',
                margin: '0.5rem 0',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                {stats.total_equippers || 0}
              </h3>
              <p style={{
                margin: 0,
                fontSize: '0.9rem',
                fontWeight: '500',
                opacity: 0.9
              }}>
                Total Equippers
              </p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card style={{
            border: 'none',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            color: 'white',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            cursor: 'default'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.12)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
          }}>
            <Card.Body style={{ padding: '1.5rem', textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                marginBottom: '0.5rem',
                opacity: 0.9
              }}>
                🏢
              </div>
              <h3 style={{
                fontSize: '2rem',
                fontWeight: '700',
                margin: '0.5rem 0',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                {stats.total_lodgers || 0}
              </h3>
              <p style={{
                margin: 0,
                fontSize: '0.9rem',
                fontWeight: '500',
                opacity: 0.9
              }}>
                Total Lodgers
              </p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card style={{
            border: 'none',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            color: 'white',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            cursor: 'default'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.12)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
          }}>
            <Card.Body style={{ padding: '1.5rem', textAlign: 'center' }}>
              <div style={{
                fontSize: '2.5rem',
                marginBottom: '0.5rem',
                opacity: 0.9
              }}>
                ⚙️
              </div>
              <h3 style={{
                fontSize: '2rem',
                fontWeight: '700',
                margin: '0.5rem 0',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                {stats.total_equipments || 0}
              </h3>
              <p style={{
                margin: 0,
                fontSize: '0.9rem',
                fontWeight: '500',
                opacity: 0.9
              }}>
                Total Equipments
              </p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Main Dashboard */}
      <Row>
        <Col>
          <Card>
            <Card.Body>
              {/* Search Box */}
              <Row className="mb-4">
                <Col md={6}>
                  <div>
                    <label className="form-label" style={{
                      fontWeight: '600',
                      color: '#495057',
                      marginBottom: '8px',
                      fontSize: '0.9rem'
                    }}>
                      Search Equipment
                    </label>
                    <div style={{ position: 'relative' }}>
                      <div style={{
                        position: 'absolute',
                        left: '12px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: '#6c757d',
                        fontSize: '16px',
                        zIndex: 1,
                        pointerEvents: 'none'
                      }}>
                        🔍
                      </div>
                      <input
                        type="text"
                        style={{
                          width: '100%',
                          padding: '12px 16px 12px 40px',
                          border: '2px solid #e9ecef',
                          borderRadius: '8px',
                          fontSize: '0.9rem',
                          backgroundColor: '#ffffff',
                          transition: 'all 0.2s ease-in-out',
                          outline: 'none',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                        }}
                        placeholder="Search by ID or Name..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#ECA869';
                          e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e9ecef';
                          e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                        }}
                      />
                      {searchTerm && (
                        <button
                          type="button"
                          onClick={() => setSearchTerm('')}
                          style={{
                            position: 'absolute',
                            right: '12px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            background: 'none',
                            border: 'none',
                            color: '#6c757d',
                            fontSize: '16px',
                            cursor: 'pointer',
                            padding: '4px',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transition: 'all 0.2s ease-in-out'
                          }}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#f8f9fa';
                            e.target.style.color = '#495057';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6c757d';
                          }}
                          onFocus={(e) => {
                            e.target.style.backgroundColor = '#f8f9fa';
                            e.target.style.color = '#495057';
                          }}
                          onBlur={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6c757d';
                          }}
                          title="Clear search"
                        >
                          ✕
                        </button>
                      )}
                    </div>
                    {searchTerm && (
                      <small style={{
                        color: '#6c757d',
                        fontSize: '0.8rem',
                        marginTop: '4px',
                        display: 'block'
                      }}>
                        Searching in: ID, Name (EN), Name (FR)
                      </small>
                    )}
                  </div>
                </Col>
                <Col md={6}>
                  {/* Clear search and filters */}
                  {(searchTerm || selectedARALevel1 || selectedARALevel2) && (
                    <div className="d-flex align-items-end h-100">
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={() => {
                          setSearchTerm('');
                          setSelectedARALevel1('');
                          setSelectedARALevel2('');
                          setCurrentPage(1);
                        }}
                        className="mb-0"
                      >
                        Clear All Filters
                      </Button>
                    </div>
                  )}
                </Col>
              </Row>

              {/* Filter Controls */}
              <Row className="mb-4">
                <Col md={6}>
                  <div>
                    <label className="form-label" style={{
                      fontWeight: '600',
                      color: '#495057',
                      marginBottom: '8px',
                      fontSize: '0.9rem'
                    }}>
                      ARA Level 1 Categories
                    </label>
                    <div style={{ position: 'relative' }}>
                      <div style={{
                        position: 'absolute',
                        left: '12px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: '#6c757d',
                        fontSize: '16px',
                        zIndex: 1,
                        pointerEvents: 'none'
                      }}>
                        📂
                      </div>
                      <Dropdown show={showLevel1Dropdown} onToggle={setShowLevel1Dropdown}>
                        <Dropdown.Toggle
                          style={{
                            width: '100%',
                            padding: '12px 40px 12px 40px',
                            border: '2px solid #e9ecef',
                            borderRadius: '8px',
                            fontSize: '0.9rem',
                            backgroundColor: '#ffffff',
                            color: '#495057',
                            textAlign: 'left',
                            transition: 'all 0.2s ease-in-out',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = '#ECA869';
                            e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = '#e9ecef';
                            e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                          }}
                        >
                          <span style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            flex: 1
                          }}>
                            {selectedARALevel1 ?
                              (araLevel1Categories.find(cat => cat.id === selectedARALevel1)?.name ||
                               araLevel1Categories.find(cat => cat.id === selectedARALevel1)?.category_name || 'Selected Category')
                              : 'All ARA Level 1 Categories'}
                          </span>
                        </Dropdown.Toggle>
                        <Dropdown.Menu className="w-100" style={{
                          maxHeight: '300px',
                          overflowY: 'auto',
                          border: '2px solid #e9ecef',
                          borderRadius: '8px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                        }}>
                          <div className="px-3 py-2">
                            <div style={{ position: 'relative' }}>
                              <div style={{
                                position: 'absolute',
                                left: '8px',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                color: '#6c757d',
                                fontSize: '14px',
                                pointerEvents: 'none'
                              }}>
                                🔍
                              </div>
                              <input
                                type="text"
                                style={{
                                  width: '100%',
                                  padding: '8px 12px 8px 28px',
                                  border: '1px solid #dee2e6',
                                  borderRadius: '6px',
                                  fontSize: '0.85rem',
                                  outline: 'none'
                                }}
                                placeholder="Search categories..."
                                value={level1Search}
                                onChange={(e) => setLevel1Search(e.target.value)}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </div>
                          </div>
                          <Dropdown.Divider />
                          <Dropdown.Item
                            onClick={() => {
                              setSelectedARALevel1('');
                              setShowLevel1Dropdown(false);
                            }}
                            style={{
                              padding: '8px 16px',
                              fontSize: '0.9rem',
                              transition: 'background-color 0.15s ease-in-out'
                            }}
                          >
                            All ARA Level 1 Categories
                          </Dropdown.Item>
                          {filteredLevel1Categories.map(category => (
                            <Dropdown.Item
                              key={category.id}
                              onClick={() => {
                                setSelectedARALevel1(category.id);
                                setShowLevel1Dropdown(false);
                              }}
                              style={{
                                padding: '8px 16px',
                                fontSize: '0.9rem',
                                transition: 'background-color 0.15s ease-in-out'
                              }}
                            >
                              {category.name || category.category_name || 'Unknown'}
                            </Dropdown.Item>
                          ))}
                          {filteredLevel1Categories.length === 0 && level1Search && (
                            <Dropdown.Item disabled style={{ padding: '8px 16px', fontSize: '0.9rem' }}>
                              No categories found
                            </Dropdown.Item>
                          )}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </div>
                </Col>
                <Col md={6}>
                  <div>
                    <label className="form-label" style={{
                      fontWeight: '600',
                      color: '#495057',
                      marginBottom: '8px',
                      fontSize: '0.9rem'
                    }}>
                      ARA Level 2 Types
                      {selectedARALevel1 && (
                        <small style={{
                          color: '#6c757d',
                          fontSize: '0.8rem',
                          marginLeft: '8px'
                        }}>
                          (filtered by selected Level 1)
                        </small>
                      )}
                    </label>
                    <div style={{ position: 'relative' }}>
                      <div style={{
                        position: 'absolute',
                        left: '12px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        color: selectedARALevel1 ? '#6c757d' : '#adb5bd',
                        fontSize: '16px',
                        zIndex: 1,
                        pointerEvents: 'none'
                      }}>
                        🏷️
                      </div>
                      <Dropdown show={showLevel2Dropdown} onToggle={setShowLevel2Dropdown}>
                        <Dropdown.Toggle
                          disabled={!selectedARALevel1 && araLevel2Types.length > 0}
                          style={{
                            width: '100%',
                            padding: '12px 40px 12px 40px',
                            border: '2px solid #e9ecef',
                            borderRadius: '8px',
                            fontSize: '0.9rem',
                            backgroundColor: selectedARALevel1 ? '#ffffff' : '#f8f9fa',
                            color: selectedARALevel1 ? '#495057' : '#6c757d',
                            textAlign: 'left',
                            transition: 'all 0.2s ease-in-out',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            cursor: selectedARALevel1 ? 'pointer' : 'not-allowed'
                          }}
                          onFocus={(e) => {
                            if (selectedARALevel1) {
                              e.target.style.borderColor = '#ECA869';
                              e.target.style.boxShadow = '0 0 0 3px rgba(236, 168, 105, 0.1)';
                            }
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = '#e9ecef';
                            e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
                          }}
                        >
                          <span style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            flex: 1
                          }}>
                            {selectedARALevel2 ?
                              (araLevel2Types.find(type => type.id === selectedARALevel2)?.name ||
                               araLevel2Types.find(type => type.id === selectedARALevel2)?.type_name || 'Selected Type')
                              : selectedARALevel1
                                ? 'All Level 2 Types for Selected Category'
                                : 'Select Level 1 Category First'}
                          </span>
                        </Dropdown.Toggle>
                        <Dropdown.Menu className="w-100" style={{
                          maxHeight: '300px',
                          overflowY: 'auto',
                          border: '2px solid #e9ecef',
                          borderRadius: '8px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                        }}>
                          {!selectedARALevel1 ? (
                            <Dropdown.Item disabled style={{ padding: '8px 16px', fontSize: '0.9rem' }}>
                              Please select an ARA Level 1 category first
                            </Dropdown.Item>
                          ) : (
                            <>
                              <div className="px-3 py-2">
                                <div style={{ position: 'relative' }}>
                                  <div style={{
                                    position: 'absolute',
                                    left: '8px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    color: '#6c757d',
                                    fontSize: '14px',
                                    pointerEvents: 'none'
                                  }}>
                                    🔍
                                  </div>
                                  <input
                                    type="text"
                                    style={{
                                      width: '100%',
                                      padding: '8px 12px 8px 28px',
                                      border: '1px solid #dee2e6',
                                      borderRadius: '6px',
                                      fontSize: '0.85rem',
                                      outline: 'none'
                                    }}
                                    placeholder='Search types...'
                                    value={level2Search}
                                    onChange={(e) => setLevel2Search(e.target.value)}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                </div>
                              </div>
                              <Dropdown.Divider />
                              <Dropdown.Item
                                onClick={() => {
                                  setSelectedARALevel2('');
                                  setShowLevel2Dropdown(false);
                                }}
                                style={{
                                  padding: '8px 16px',
                                  fontSize: '0.9rem',
                                  transition: 'background-color 0.15s ease-in-out'
                                }}
                              >
                                All Level 2 Types for Selected Category
                              </Dropdown.Item>
                              {filteredLevel2Types.map(type => (
                                <Dropdown.Item
                                  key={type.id}
                                  onClick={() => {
                                    setSelectedARALevel2(type.id);
                                    setShowLevel2Dropdown(false);
                                  }}
                                  style={{
                                    padding: '8px 16px',
                                    fontSize: '0.9rem',
                                    transition: 'background-color 0.15s ease-in-out'
                                  }}
                                >
                                  {type.name || type.type_name || 'Unknown'}
                                </Dropdown.Item>
                              ))}
                              {filteredLevel2Types.length === 0 && level2Search && (
                                <Dropdown.Item disabled style={{ padding: '8px 16px', fontSize: '0.9rem' }}>
                                  No types found for this category
                                </Dropdown.Item>
                              )}
                              {filteredLevel2Types.length === 0 && !level2Search && (
                                <Dropdown.Item disabled style={{ padding: '8px 16px', fontSize: '0.9rem' }}>
                                  No Level 2 types available for this category
                                </Dropdown.Item>
                              )}
                            </>
                          )}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </div>
                </Col>
              </Row>

              {/* Filter Status and Clear Button */}
              {(selectedARALevel1 || selectedARALevel2) && (
                <Row className="mb-3">
                  <Col>
                    <div className="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                      <div>
                        <small className="text-muted">
                          <strong>Active Filters:</strong>
                          {selectedARALevel1 && (
                            <span className="ms-2 badge bg-primary">
                              Level 1: {araLevel1Categories.find(cat => cat.id === selectedARALevel1)?.name ||
                                       araLevel1Categories.find(cat => cat.id === selectedARALevel1)?.category_name}
                            </span>
                          )}
                          {selectedARALevel2 && (
                            <span className="ms-2 badge bg-secondary">
                              Level 2: {araLevel2Types.find(type => type.id === selectedARALevel2)?.name ||
                                       araLevel2Types.find(type => type.id === selectedARALevel2)?.type_name}
                            </span>
                          )}
                        </small>
                      </div>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={() => {
                          setSelectedARALevel1('');
                          setSelectedARALevel2('');
                        }}
                      >
                        Clear All Filters
                      </Button>
                    </div>
                  </Col>
                </Row>
              )}

              {filteredInventory.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted">
                    {(selectedARALevel1 || selectedARALevel2)
                      ? 'No items match the selected filters. Try adjusting your filter criteria.'
                      : 'No master inventory items found.'
                    }
                  </p>
                  {(selectedARALevel1 || selectedARALevel2) && (
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={() => {
                        setSelectedARALevel1('');
                        setSelectedARALevel2('');
                      }}
                    >
                      Clear Filters
                    </Button>
                  )}
                </div>
              ) : (
                <div>
                  {/* Results counter */}
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <span className="text-muted">
                        Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredInventory.length)} of {filteredInventory.length} equipment
                        {searchTerm && (
                          <span className="ms-2">
                            <strong>(filtered by search: &quot;{searchTerm}&quot;)</strong>
                          </span>
                        )}
                        {(selectedARALevel1 || selectedARALevel2) && (
                          <span className="ms-2">
                            <strong>(filtered by ARA levels)</strong>
                          </span>
                        )}
                      </span>
                    </div>
                  </div>

                  <div className="table-responsive" style={{
                    borderRadius: '8px',
                    overflow: 'hidden',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    border: '1px solid #e9ecef'
                  }}>
                    <Table hover className="mb-0" style={{
                      backgroundColor: 'white',
                      borderCollapse: 'separate',
                      borderSpacing: '0'
                    }}>
                    <thead style={{ backgroundColor: '#ECA869', color: 'black' }}>
                      <tr>
                        <th>ID</th>
                        <th>Name (EN)</th>
                        <th>Name (FR)</th>
                        <th>ARA Level 1</th>
                        <th>ARA Level 2</th>
                        <th>Image</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paginatedData.map((item, index) => (
                        <tr key={item.id || index} style={{
                          borderBottom: '1px solid #e9ecef',
                          transition: 'background-color 0.15s ease-in-out'
                        }}>
                          <td style={{
                            padding: '12px 8px',
                            fontSize: '0.85rem',
                            fontFamily: 'monospace',
                            color: '#495057',
                            fontWeight: '500'
                          }}>{item.id || '-'}</td>
                          <td style={{
                            padding: '12px 12px',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            color: '#212529'
                          }}>{item.name_en || '-'}</td>
                          <td style={{
                            padding: '12px 12px',
                            fontSize: '0.9rem',
                            color: '#495057'
                          }}>{item.name_fr || '-'}</td>
                          <td style={{
                            padding: '12px 12px',
                            fontSize: '0.85rem'
                          }}>
                            {item.ara_level1_name ? (
                              <span style={{
                                backgroundColor: '#e3f2fd',
                                color: '#1565c0',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                fontSize: '0.8rem',
                                fontWeight: '500'
                              }}>
                                {item.ara_level1_name}
                              </span>
                            ) : (
                              <span style={{ color: '#6c757d' }}>-</span>
                            )}
                          </td>
                          <td style={{
                            padding: '12px 12px',
                            fontSize: '0.85rem'
                          }}>
                            {item.ara_level2_name ? (
                              <span style={{
                                backgroundColor: '#f3e5f5',
                                color: '#7b1fa2',
                                padding: '4px 8px',
                                borderRadius: '12px',
                                fontSize: '0.8rem',
                                fontWeight: '500'
                              }}>
                                {item.ara_level2_name}
                              </span>
                            ) : (
                              <span style={{ color: '#6c757d' }}>-</span>
                            )}
                          </td>
                          <td style={{
                            padding: '12px 8px',
                            textAlign: 'center'
                          }}>
                            {item.image_link ? (
                              <div
                                style={{
                                  position: 'relative',
                                  display: 'inline-block',
                                  borderRadius: '8px',
                                  overflow: 'hidden',
                                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                  cursor: 'pointer'
                                }}
                                onClick={() => handleImageClick(item.image_link, item.name_en, item.id)}
                                title="Click to view full size"
                              >
                                <img
                                  src={item.image_link}
                                  alt={item.name_en || 'Equipment'}
                                  style={{
                                    width: '60px',
                                    height: '60px',
                                    objectFit: 'cover',
                                    display: 'block',
                                    transition: 'transform 0.2s ease-in-out'
                                  }}
                                  onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.parentNode.innerHTML = '<div style="width:60px;height:60px;background:#f8f9fa;border:2px dashed #dee2e6;border-radius:8px;display:flex;align-items:center;justify-content:center;font-size:0.7rem;color:#6c757d;font-weight:500;">No Image</div>';
                                  }}
                                  onMouseOver={(e) => {
                                    e.target.style.transform = 'scale(1.05)';
                                  }}
                                  onMouseOut={(e) => {
                                    e.target.style.transform = 'scale(1)';
                                  }}
                                  onFocus={(e) => {
                                    e.target.style.transform = 'scale(1.05)';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.transform = 'scale(1)';
                                  }}
                                />
                                {/* Click indicator overlay */}
                                <div style={{
                                  position: 'absolute',
                                  top: '2px',
                                  right: '2px',
                                  backgroundColor: 'rgba(0,0,0,0.7)',
                                  color: 'white',
                                  borderRadius: '50%',
                                  width: '16px',
                                  height: '16px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '10px',
                                  fontWeight: 'bold'
                                }}>
                                  🔍
                                </div>
                              </div>
                            ) : (
                              <div style={{
                                width: '60px',
                                height: '60px',
                                backgroundColor: '#f8f9fa',
                                border: '2px dashed #dee2e6',
                                borderRadius: '8px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '0.7rem',
                                color: '#6c757d',
                                fontWeight: '500'
                              }}>
                                No Image
                              </div>
                            )}
                          </td>
                          <td style={{
                            padding: '12px 8px',
                            textAlign: 'center',
                            verticalAlign: 'middle'
                          }}>
                            <div style={{ display: 'flex', gap: '8px', justifyContent: 'center', alignItems: 'center' }}>
                              {/* Edit Button */}
                              <button
                                onClick={() => window.location.href = `/admin/master-equipment/${item.id}`}
                                style={{
                                  backgroundColor: '#ECA869',
                                  color: 'white',
                                  border: '2px solid #ECA869',
                                  borderRadius: '8px',
                                  padding: '8px 12px',
                                  fontSize: '0.875rem',
                                  fontWeight: '500',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s ease-in-out',
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  gap: '4px',
                                  minWidth: '70px',
                                  height: '36px',
                                  boxShadow: '0 2px 4px rgba(236, 168, 105, 0.2)'
                                }}
                                onMouseOver={(e) => {
                                  e.target.style.backgroundColor = '#d4956b';
                                  e.target.style.borderColor = '#d4956b';
                                  e.target.style.transform = 'translateY(-1px)';
                                  e.target.style.boxShadow = '0 4px 8px rgba(236, 168, 105, 0.3)';
                                }}
                                onMouseOut={(e) => {
                                  e.target.style.backgroundColor = '#ECA869';
                                  e.target.style.borderColor = '#ECA869';
                                  e.target.style.transform = 'translateY(0)';
                                  e.target.style.boxShadow = '0 2px 4px rgba(236, 168, 105, 0.2)';
                                }}
                                onFocus={(e) => {
                                  e.target.style.backgroundColor = '#d4956b';
                                  e.target.style.borderColor = '#d4956b';
                                  e.target.style.outline = '2px solid rgba(236, 168, 105, 0.4)';
                                  e.target.style.outlineOffset = '2px';
                                }}
                                onBlur={(e) => {
                                  e.target.style.backgroundColor = '#ECA869';
                                  e.target.style.borderColor = '#ECA869';
                                  e.target.style.outline = 'none';
                                }}
                                title="Edit equipment details"
                              >
                                <span style={{ fontSize: '14px' }}>✏️</span>
                                <span>Edit</span>
                              </button>

                              {/* Classify Button - Only show if equipment lacks ARA classifications */}
                              {(!item.ara_level1_name || !item.ara_level2_name) && (
                                <button
                                  onClick={() => handleClassifyClick(item)}
                                  style={{
                                    backgroundColor: '#28a745',
                                    color: 'white',
                                    border: '2px solid #28a745',
                                    borderRadius: '8px',
                                    padding: '8px 12px',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    cursor: 'pointer',
                                    transition: 'all 0.2s ease-in-out',
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    gap: '4px',
                                    minWidth: '80px',
                                    height: '36px',
                                    boxShadow: '0 2px 4px rgba(40, 167, 69, 0.2)'
                                  }}
                                  onMouseOver={(e) => {
                                    e.target.style.backgroundColor = '#218838';
                                    e.target.style.borderColor = '#218838';
                                    e.target.style.transform = 'translateY(-1px)';
                                    e.target.style.boxShadow = '0 4px 8px rgba(40, 167, 69, 0.3)';
                                  }}
                                  onMouseOut={(e) => {
                                    e.target.style.backgroundColor = '#28a745';
                                    e.target.style.borderColor = '#28a745';
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = '0 2px 4px rgba(40, 167, 69, 0.2)';
                                  }}
                                  onFocus={(e) => {
                                    e.target.style.backgroundColor = '#218838';
                                    e.target.style.borderColor = '#218838';
                                    e.target.style.outline = '2px solid rgba(40, 167, 69, 0.4)';
                                    e.target.style.outlineOffset = '2px';
                                  }}
                                  onBlur={(e) => {
                                    e.target.style.backgroundColor = '#28a745';
                                    e.target.style.borderColor = '#28a745';
                                    e.target.style.outline = 'none';
                                  }}
                                  title="Classify equipment using AI"
                                >
                                  <span style={{ fontSize: '14px' }}>🤖</span>
                                  <span>Classify</span>
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                  </div>
                </div>
              )}

              {/* Pagination Controls */}
              {filteredInventory.length > itemsPerPage && (
                <div className="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                  <div>
                    <small className="text-muted">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredInventory.length)} of {filteredInventory.length} entries
                      {(selectedARALevel1 || selectedARALevel2) && (
                        <span> (filtered from {masterInventory.length} total)</span>
                      )}
                    </small>
                  </div>
                  <div className="d-flex gap-1 align-items-center">
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(1)}
                    >
                      First
                    </Button>
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(currentPage - 1)}
                    >
                      Previous
                    </Button>
                    <span className="mx-2">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      disabled={currentPage === totalPages}
                      onClick={() => setCurrentPage(currentPage + 1)}
                    >
                      Next
                    </Button>
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      disabled={currentPage === totalPages}
                      onClick={() => setCurrentPage(totalPages)}
                    >
                      Last
                    </Button>
                  </div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
      </Container>

      {/* Image Modal */}
      <Modal
        show={showImageModal}
        onHide={handleCloseModal}
        size="lg"
        centered
        backdrop="static"
      >
        <Modal.Header closeButton style={{ backgroundColor: '#061C3D', color: 'white' }}>
          <Modal.Title style={{ fontSize: '1.1rem', fontWeight: '600' }}>
            {selectedImage.title}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ padding: '0', backgroundColor: '#f8f9fa' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px',
            padding: '20px'
          }}>
            {selectedImage.src ? (
              <img
                src={selectedImage.src}
                alt={selectedImage.alt}
                style={{
                  maxWidth: '100%',
                  maxHeight: '70vh',
                  objectFit: 'contain',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                }}
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            ) : null}
            <div
              style={{
                display: 'none',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                color: '#6c757d',
                fontSize: '1.1rem'
              }}
            >
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📷</div>
              <div>Image could not be loaded</div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer style={{ backgroundColor: '#f8f9fa', borderTop: '1px solid #dee2e6' }}>
          <Button
            variant="secondary"
            onClick={handleCloseModal}
            style={{
              backgroundColor: '#6c757d',
              borderColor: '#6c757d',
              fontWeight: '500'
            }}
          >
            Close
          </Button>
          {selectedImage.src && (
            <Button
              variant="primary"
              onClick={() => window.open(selectedImage.src, '_blank')}
              style={{
                backgroundColor: '#061C3D',
                borderColor: '#061C3D',
                fontWeight: '500'
              }}
            >
              Open in New Tab
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Classification Modal */}
      <ClassificationModal
        show={showClassificationModal}
        onClose={handleCloseClassificationModal}
        equipmentId={selectedEquipmentForClassification?.id}
        equipmentName={selectedEquipmentForClassification?.name_en}
        onSuccess={handleClassificationSuccess}
      />
    </Container>
  );
}

export default AdminDashboard;
