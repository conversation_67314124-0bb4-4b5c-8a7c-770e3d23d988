import React, { useState } from 'react';
import PropTypes from 'prop-types';
import RenderIf from '../../shared/components/Render_if';
import CustomButton from '../../shared/components/buttons/Custom_button';
import axios from 'axios';
import { getCookies } from '../../shared/helpers/Cookies_helper';

export default function ClassificationModal({
  show,
  onClose,
  equipmentId,
  equipmentName,
  onSuccess
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [stats, setStats] = useState(null);

  const handleClassify = async () => {
    setIsLoading(true);
    setError('');
    setSuccess(false);

    try {
      const token = getCookies('adminToken');
      const config = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      };

      const baseURL = import.meta.env.VITE_REACT_APP_BASE_URL;
      const response = await axios.post(
        `${baseURL}/admin/master-equipment/${equipmentId}/classify`,
        {},
        config
      );

      setStats(response.data.stats);
      setSuccess(true);
      
      // Call onSuccess callback to refresh the parent component
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Classification error:', err);
      setError(
        err.response?.data?.error || 
        'Failed to classify equipment. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess(false);
    setStats(null);
    onClose();
  };

  return (
    <RenderIf condition={show}>
      <div className="modal">
        <div className="modal-content no-title-margeTop delete-modal">
          <button
            className="close-button"
            onClick={handleClose}
            disabled={isLoading}
          >
            <svg
              width="25"
              height="24"
              viewBox="0 0 25 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19.25 5.25L5.75 18.75"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M19.25 18.75L5.75 5.25"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          
          <div className="row">
            <div className="col-lg-10 mx-auto">
              <div className="mt-4">
                {!success && !error && (
                  <>
                    <h4 className="t-header-medium c-grey-titles mb-3">
                      🤖 Classify Equipment
                    </h4>
                    <p className="t-base c-grey-titles mb-3">
                      Are you sure you want to classify this equipment using AI?
                    </p>
                    <div style={{
                      backgroundColor: '#f8f9fa',
                      padding: '16px',
                      borderRadius: '8px',
                      border: '1px solid #e9ecef',
                      marginBottom: '20px'
                    }}>
                      <p className="t-subheading-3 c-fake-black mb-2">
                        <strong>Equipment:</strong> {equipmentName || 'Unknown'}
                      </p>
                      <p className="t-subheading-3 c-fake-black mb-0">
                        <strong>ID:</strong> {equipmentId}
                      </p>
                    </div>
                    <p className="t-small c-grey-titles">
                      This will automatically assign ARA Level 1 and Level 2 classifications 
                      to this equipment using AI analysis.
                    </p>
                  </>
                )}

                {error && (
                  <div style={{
                    backgroundColor: '#f8d7da',
                    color: '#721c24',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #f5c6cb',
                    marginBottom: '20px'
                  }}>
                    <h5 className="mb-2">❌ Classification Failed</h5>
                    <p className="mb-0">{error}</p>
                  </div>
                )}

                {success && stats && (
                  <div style={{
                    backgroundColor: '#d4edda',
                    color: '#155724',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #c3e6cb',
                    marginBottom: '20px'
                  }}>
                    <h5 className="mb-3">✅ Classification Successful!</h5>
                    <div className="row">
                      <div className="col-md-6">
                        <p className="mb-1"><strong>Equipment Processed:</strong> {stats.total_equipment || 1}</p>
                        <p className="mb-1"><strong>Successfully Classified:</strong> {stats.successful_classifications || 0}</p>
                      </div>
                      <div className="col-md-6">
                        <p className="mb-1"><strong>Processing Time:</strong> {stats.processing_time || 'N/A'}</p>
                        <p className="mb-1"><strong>Cost:</strong> ${stats.total_cost?.toFixed(4) || '0.0000'}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="text-center btn-content fixed-button-modal">
                {!success && (
                  <>
                    <button
                      className="round-button black bold"
                      onClick={handleClose}
                      disabled={isLoading}
                    >
                      Cancel
                    </button>

                    <CustomButton
                      className="round-button yellow bold"
                      isLoading={isLoading}
                      disabled={isLoading}
                      onClick={handleClassify}
                      textButton="🤖 Classify Equipment"
                    />
                  </>
                )}

                {success && (
                  <button
                    className="round-button yellow bold"
                    onClick={handleClose}
                  >
                    Done
                  </button>
                )}

                {error && (
                  <>
                    <button
                      className="round-button black bold"
                      onClick={handleClose}
                    >
                      Cancel
                    </button>

                    <CustomButton
                      className="round-button yellow bold"
                      isLoading={isLoading}
                      disabled={isLoading}
                      onClick={handleClassify}
                      textButton="🔄 Try Again"
                    />
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </RenderIf>
  );
}

ClassificationModal.propTypes = {
  show: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  equipmentId: PropTypes.string.isRequired,
  equipmentName: PropTypes.string,
  onSuccess: PropTypes.func
};

ClassificationModal.defaultProps = {
  equipmentName: 'Unknown Equipment',
  onSuccess: null
};
