import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import axios from 'axios';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { withStyles } from '@mui/styles';
import Box from '@mui/material/Box';
import Step from '@mui/material/Step';
import StepConnector from '@mui/material/StepConnector';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import Typography from '@mui/material/Typography';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import CustomButton from '../../../../shared/components/buttons/Custom_button.jsx';
import RenderIf from '../../../../shared/components/Render_if.jsx';
import SuccessPopUp from '../../../../shared/components/modals/Success_pop_up.jsx';
import Popup from '../../../../shared/components/modals/Popup.jsx';
import StepperMobile from '../../../../shared/components/mobile_stepper/Mobile_stepper.jsx';

import { getSessionStorage } from '../../../../shared/helpers/Session_storage_helper.js';
import { getCookies, setCookies } from '../../../../shared/helpers/Cookies.js';
import { EQUIPMENT_UPLOAD_IMAGE } from '../../../../shared/helpers/Url_constants.js';
import useResponsive from '../../../../shared/helpers/Responsive.js';
import { useEquipment } from '../../../../shared/context/Equipment_context.jsx';

import equipment from '../equipment.model.json';
import Step1 from './Step1.jsx';
import Step2 from './Step2.jsx';
import Step3 from './Step3.jsx';

async function getFileFromBlob(imageLink) {
  const response = await fetch(imageLink);
  const blob = await response?.blob();
  return new File([blob], 'image.jpg', { type: blob.type });
}

export default function AddEquipment({ t, detectLanguage }) {
  const { AddSingleEquipment } = useEquipment();
  const equipper = getSessionStorage('equipper');
  if (!equipper) {
    useNavigate('/');
  }
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [id, setId] = useState(null);

  const [showError, setShowError] = useState(false);
  const [serverResponse, setServerResponse] = useState(false);
  useEffect(() => {
    setDescriptionInputName(
      detectLanguage === 'fr' ? 'description_fr' : 'description'
    );
  }, [detectLanguage]);

  const onCloseSuccess = () => {
    setShowSuccessModal(false);
    navigate(
      `/equipperManagementPortal/equipmentManagement/equipmentDetails/${id}`
    );
  };
  const onCloseError = () => {
    setShowError(false);
  };
  const [descriptionInputName, setDescriptionInputName] = useState('');
  const [aliasEn, setAliasEn] = useState([]);
  const [equipmentName, setEquipmentName] = useState({});
  const normalizeString = (str) => str.replace(/[\s-_]+/g, '').toLowerCase();

  // Function to check if the preferred name matches the main name or aliasesconst normalizeString = (str) => {

  const isPreferredNameMatch = (preferred_equipment_name) => {
    if (!preferred_equipment_name) return false;

    const normalizedPreferredName = normalizeString(preferred_equipment_name);
    const normalizedEquipmentName = normalizeString(
      equipmentName.requestedName
    );

    // Compare with the main equipment name (case-insensitive)
    if (normalizedPreferredName === normalizedEquipmentName) {
      return true;
    }

    // Compare with aliases (case-insensitive)
    if (aliasEn && aliasEn.length > 0) {
      return aliasEn.some(
        (alias) => normalizeString(alias) === normalizedPreferredName
      );
    }

    // No match found
    return false;
  };

  const initialValues = {
    ...equipment,
    equipper_id: equipper.id,
    address: equipper.address,
    equipper_name: equipper.company,
    equipper_email: equipper.email,
    status: 'available',
    price: {
      currency: equipper.currency
    },
    specifications: [
      {
        specification: descriptionInputName,
        value: ''
      }
    ],
    preferred_equipment_name: ''
  };

  const validationSchema = Yup.object().shape({
    internal_id: Yup.string()
      .required(t('Internal_id_required'))
      .matches(/^(?!.*\|\|\|).*$/, t('Invalid_internal_id')),
    preferred_equipment_name: Yup.string().test(
      'matches-equipment-or-alias',
      t('Preferred_equipment_name_error'),
      function validatePreferredName(value) {
        return !isPreferredNameMatch(value);
      }
    ),
    price: Yup.object({
      day: Yup.number()
        .required(t('Price_required'))
        .positive(t('Price_positive')),
      week: Yup.number()
        .required(t('Price_required'))
        .positive(t('Price_positive')),
      month: Yup.number()
        .required(t('Price_required'))
        .positive(t('Price_positive'))
    }),
    minimum_rental_period: Yup.number()
      .min(0, t('Minimum_rental_period_must_be_greater_than_0'))
      .required(t('This_field_is_required')),
    specifications: Yup.array()
      .of(
        Yup.object().shape({
          specification: Yup.string().test(
            'specification',
            t('Specifications_required'),
            (value, context) => {
              const { path } = context;
              const index = parseInt(path.split('[')[1].split(']')[0]);
              return index === 0 || value !== undefined;
            }
          ),
          value: Yup.string().test(
            'value',
            t('Specifications_value_required'),
            (value, context) => {
              const { path } = context;
              const index = parseInt(path.split('[')[1].split(']')[0]);
              return index === 0 || value !== undefined;
            }
          )
        })
      )
      .test('specifications', 'Specifications are required', (specs) => {
        if (!specs || specs.length === 0) {
          return true; // Allow empty array
        }
        return specs.slice(1).every((spec) => spec.specification && spec.value);
      })
  });

  const [nextButtonDisabled, setNextButtonDisabled] = useState(true);
  const [activeStep, setActiveStep] = useState(0);
  const navigate = useNavigate();

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = async (values, actions) => {
    let equipper_equipment_picture = null;
    if (values?.equipper_equipment_picture?.startsWith('blob')) {
      equipper_equipment_picture = await getFileFromBlob(
        values.equipper_equipment_picture
      );
      values.equipper_equipment_picture = '';
    }
    
    // Convert type_of_propulsion to array if it's a string
    if (values.type_of_propulsion && typeof values.type_of_propulsion === 'string') {
      values.type_of_propulsion = [values.type_of_propulsion];
    } else if (!values.type_of_propulsion) {
      values.type_of_propulsion = [];
    }
    
    const res = await AddSingleEquipment(values);
    if (res?.status === 200 && res?.data !== null) {
      setId(res?.data?.id);
      setCookies('has_inventory', true);

      if (equipper_equipment_picture) {
        await uploadImage(equipper_equipment_picture, res?.data?.id);
      }

      setShowSuccessModal(true);

      actions?.setSubmitting(false);
    } else {
      setShowError(true);
      setServerResponse(res);
      actions?.setSubmitting(false);
    }
  };

  const uploadImage = async (file, equipmetID) => {
    const formatData = new FormData();

    formatData.set('file', file);

    const header = {
      'Content-Type': 'multipart/form-data'
    };
    header.Authorization = `Bearer ${getCookies('token')}`;

    await axios.post(
      `${
        import.meta.env.VITE_REACT_APP_BASE_URL + EQUIPMENT_UPLOAD_IMAGE
      }/${equipmetID}`,
      formatData,
      { headers: header }
    );
  };

  const steps = [
    t('Equipment_information'),
    t('Status_price'),
    t('Equipment_specifications').replace(':', ' ')
  ];

  const handleCancel = () => {
    navigate('/equipperManagementPortal/equipmentManagement/addEquipment');
  };

  const QontoConnector = withStyles({
    active: {
      '& $line': {
        borderColor: '#ECA869'
      }
    },
    completed: {
      '& $line': {
        borderColor: '#ECA869'
      }
    },
    line: {
      borderTopWidth: 3,
      borderRadius: 1
    }
  })(StepConnector);

  const theme = createTheme({
    palette: {
      primary: { main: '#ECA869' }
    },
    components: {
      MuiStepIcon: {
        styleOverrides: {
          text: {
            fill: 'white'
          }
        }
      },
      MuiStepLabel: {
        styleOverrides: {
          label: {
            fontFamily: 'inherit'
          }
        }
      }
    }
  });

  const { isMobile, isTablet } = useResponsive();
  return (
    <Box display="flex" flexDirection="column" marginTop="3%">
      <Box maxWidth="900px" className="container d-lg-block d-none">
        <ThemeProvider theme={theme}>
          <Stepper activeStep={activeStep} connector={<QontoConnector />}>
            {steps.map((label, index) => (
              <Step key={index}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </ThemeProvider>
      </Box>
      <Box>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={(values, actions) => handleSubmit(values, actions)}
          isInitialValid={true}
        >
          {(formik) => (
            <Form>
              <RenderIf condition={isMobile || isTablet}>
                <StepperMobile
                  steps={steps}
                  setActiveStep={setActiveStep}
                  nextButtonDisabled={nextButtonDisabled}
                  handleCancel={handleCancel}
                  handleSubmit={handleSubmit}
                  formik={formik}
                  disabled={!formik.isValid || formik.isSubmitting}
                  t={t}
                  activeStep={activeStep}
                />
              </RenderIf>
              <Box
                display="flex"
                className="container"
                flexDirection="column"
                justifyContent="center"
                alignItems="center"
                marginTop="3%"
              >
                {
                  <Typography fontWeight="bold" variant="h6">
                    {steps[activeStep]}
                  </Typography>
                }

                <Typography variant="h10">
                  {t('Please_fill_in_fields')}
                </Typography>
                <hr
                  style={{
                    width: '75%',
                    color: 'Fill (919px)',
                    height: '1px'
                  }}
                />
              </Box>
              <Box>
                <div>
                  {activeStep === 0 && (
                    <Step1
                      formik={formik}
                      t={t}
                      smallDevice={isMobile || isTablet}
                      detectLanguage={detectLanguage}
                      setNextButtonDisabled={setNextButtonDisabled}
                      setEquipmentName={setEquipmentName}
                      setAliasEn={setAliasEn}
                      equipmentName={equipmentName}
                    />
                  )}
                  {activeStep === 1 && (
                    <Step2
                      formik={formik}
                      t={t}
                      setNextButtonDisabled={setNextButtonDisabled}
                    />
                  )}
                  {activeStep === 2 && (
                    <Step3
                      formik={formik}
                      t={t}
                      detectLanguage={detectLanguage}
                    />
                  )}
                </div>

                <Box
                  display="flex"
                  flexDirection="row"
                  gap="10%"
                  alignContent="center"
                  justifyContent="space-between"
                  marginRight="8%"
                  marginTop="4%"
                  marginLeft="8%"
                >
                  <Box className="d-lg-flex d-none  align-items-center">
                    <RenderIf condition={activeStep !== 0}>
                      <CustomButton
                        textButton={
                          <FontAwesomeIcon
                            icon="arrow-left"
                            className="c-black"
                          />
                        }
                        onClick={handleBack}
                        className="round-button transparent black "
                      />
                    </RenderIf>

                    <CustomButton
                      textButton={t('Cancel')}
                      onClick={handleCancel}
                      className="round-button transparent black "
                    />
                  </Box>

                  <RenderIf condition={activeStep === steps.length - 1}>
                    <CustomButton
                      textPopper={
                        !formik.isValid ? t('Please_fill_in_fields') : ''
                      }
                      textButton={t('Submit')}
                      type="submit"
                      variant="contained"
                      className="round-button yellow c-black c-black d-lg-block d-none"
                      color="primary"
                      disabled={!formik.isValid || formik.isSubmitting}
                      isLoading={formik.isSubmitting}
                    />
                  </RenderIf>

                  <RenderIf condition={activeStep !== steps.length - 1}>
                    <CustomButton
                      textPopper={
                        nextButtonDisabled ? t('Please_fill_in_fields') : ''
                      }
                      textButton={t('Next')}
                      variant="contained"
                      className="round-button yellow c-black d-lg-block d-none "
                      color="primary"
                      onClick={() => {
                        setActiveStep((prevActiveStep) => prevActiveStep + 1);
                        setNextButtonDisabled(false);
                      }}
                      disabled={nextButtonDisabled}
                    />
                  </RenderIf>
                </Box>
              </Box>
              <SuccessPopUp show={showSuccessModal} onClose={onCloseSuccess} />
              <Popup
                show={showError}
                response={serverResponse}
                onClose={onCloseError}
                t={t}
              />
            </Form>
          )}
        </Formik>
      </Box>
    </Box>
  );
}
