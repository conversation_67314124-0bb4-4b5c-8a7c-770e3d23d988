#!/usr/bin/env node

/**
 * Integration test script for Admin Master Equipment Update functionality
 * This script tests the complete workflow of updating master equipment through the admin interface
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.VITE_REACT_APP_BASE_URL || 'http://localhost:8080';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';

let adminToken = '';

// Test data
const testEquipmentUpdate = {
  name_en: 'Updated Test Equipment',
  name_fr: 'Équipement de Test Mis à Jour',
  description: 'Updated description for testing',
  description_en: 'Updated English description',
  description_fr: 'Description française mise à jour',
  brand: 'Updated Test Brand',
  model: 'Updated Test Model',
  category: ['Construction', 'Heavy Machinery'],
  sub_category: ['Excavators', 'Bulldozers'],
  alias: {
    en: ['updated-alias-en', 'test-equipment-en'],
    fr: ['alias-mis-à-jour-fr', 'équipement-test-fr']
  },
  image_link: 'https://example.com/updated-image.jpg',
  ara_level1_id: 1,
  ara_level2_id: 2,
  ara_level1_name: 'Test ARA Level 1',
  ara_level2_name: 'Test ARA Level 2'
};

// Helper functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const makeRequest = async (method, url, data = null, headers = {}) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
};

// Test functions
const testAdminLogin = async () => {
  log('Testing admin login...');
  
  const result = await makeRequest('POST', '/admin/signin', {
    email: ADMIN_EMAIL,
    password: ADMIN_PASSWORD
  });

  if (!result.success) {
    throw new Error(`Admin login failed: ${JSON.stringify(result.error)}`);
  }

  adminToken = result.data.token;
  log('Admin login successful', 'success');
  return adminToken;
};

const testGetMasterInventory = async () => {
  log('Testing get master inventory...');
  
  const result = await makeRequest('GET', '/admin/master-inventory', null, {
    Authorization: `Bearer ${adminToken}`
  });

  if (!result.success) {
    throw new Error(`Get master inventory failed: ${JSON.stringify(result.error)}`);
  }

  const inventory = result.data.data || [];
  log(`Retrieved ${inventory.length} master equipment items`, 'success');
  
  if (inventory.length === 0) {
    throw new Error('No master equipment found for testing');
  }

  return inventory[0]; // Return first item for testing
};

const testGetMasterEquipmentByID = async (equipmentId) => {
  log(`Testing get master equipment by ID: ${equipmentId}...`);
  
  const result = await makeRequest('GET', `/admin/master-equipment/${equipmentId}`, null, {
    Authorization: `Bearer ${adminToken}`
  });

  if (!result.success) {
    throw new Error(`Get master equipment by ID failed: ${JSON.stringify(result.error)}`);
  }

  log('Successfully retrieved master equipment by ID', 'success');
  return result.data;
};

const testUpdateMasterEquipment = async (equipmentId, originalData) => {
  log(`Testing update master equipment: ${equipmentId}...`);
  
  // Merge original data with test updates
  const updateData = {
    ...originalData,
    ...testEquipmentUpdate,
    id: equipmentId
  };

  const result = await makeRequest('PUT', `/admin/master-equipment/${equipmentId}`, updateData, {
    Authorization: `Bearer ${adminToken}`
  });

  if (!result.success) {
    throw new Error(`Update master equipment failed: ${JSON.stringify(result.error)}`);
  }

  log('Successfully updated master equipment', 'success');
  return result.data;
};

const testValidationErrors = async (equipmentId) => {
  log('Testing validation errors...');
  
  // Test with invalid data
  const invalidData = {
    id: equipmentId,
    name_en: '', // Empty required field
    name_fr: '', // Empty required field
    image_link: 'invalid-url', // Invalid URL
    ara_level1_id: -1, // Negative ID
    ara_level2_id: -1  // Negative ID
  };

  const result = await makeRequest('PUT', `/admin/master-equipment/${equipmentId}`, invalidData, {
    Authorization: `Bearer ${adminToken}`
  });

  if (result.success) {
    throw new Error('Validation should have failed but didn\'t');
  }

  if (result.status !== 400) {
    throw new Error(`Expected 400 status for validation error, got ${result.status}`);
  }

  log('Validation errors correctly handled', 'success');
};

const testUnauthorizedAccess = async (equipmentId) => {
  log('Testing unauthorized access...');
  
  const result = await makeRequest('GET', `/admin/master-equipment/${equipmentId}`);

  if (result.success) {
    throw new Error('Unauthorized access should have failed but didn\'t');
  }

  if (result.status !== 401) {
    throw new Error(`Expected 401 status for unauthorized access, got ${result.status}`);
  }

  log('Unauthorized access correctly blocked', 'success');
};

const testNotFound = async () => {
  log('Testing not found error...');
  
  const result = await makeRequest('GET', '/admin/master-equipment/non-existent-id', null, {
    Authorization: `Bearer ${adminToken}`
  });

  if (result.success) {
    throw new Error('Not found should have failed but didn\'t');
  }

  if (result.status !== 500 && result.status !== 404) {
    log(`Note: Expected 404 or 500 status for not found, got ${result.status}`, 'info');
  }

  log('Not found error correctly handled', 'success');
};

// Main test runner
const runTests = async () => {
  try {
    log('Starting Admin Master Equipment Update tests...');
    
    // Test 1: Admin login
    await testAdminLogin();
    
    // Test 2: Get master inventory
    const testEquipment = await testGetMasterInventory();
    const equipmentId = testEquipment.id;
    
    // Test 3: Get master equipment by ID
    const originalData = await testGetMasterEquipmentByID(equipmentId);
    
    // Test 4: Update master equipment
    await testUpdateMasterEquipment(equipmentId, originalData);
    
    // Test 5: Validation errors
    await testValidationErrors(equipmentId);
    
    // Test 6: Unauthorized access
    await testUnauthorizedAccess(equipmentId);
    
    // Test 7: Not found error
    await testNotFound();
    
    log('All tests completed successfully! 🎉', 'success');
    
  } catch (error) {
    log(`Test failed: ${error.message}`, 'error');
    process.exit(1);
  }
};

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testAdminLogin,
  testGetMasterInventory,
  testGetMasterEquipmentByID,
  testUpdateMasterEquipment,
  testValidationErrors,
  testUnauthorizedAccess,
  testNotFound
};
